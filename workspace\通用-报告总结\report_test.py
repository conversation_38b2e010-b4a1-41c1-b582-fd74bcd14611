# -*- coding: utf-8 -*-
import random
import sys
import os

import requests
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import numpy as np
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from tools.numpy_2_python import convert_numpy_types
from utils.agent_tester import AgentTester

def report_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVQXhMb3lCVjU0bVN6ZmZOQklJWHh2QkJ5K0NiaFZNRzd1dXJqR3V0Q2lHemc9PSJ9"
    # agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJNVmZTOHF3ZTVGU3hqZkVJTzBrZ01SNTJ1SUhTSmtOQzMvemRJdDV0QUgwNHVjenptZjc2anREcE5HWjY4K29uSjRuazNXV3ZUVnphcmRFMGRjZzVqK09KNllvK3doZ1AvUlduaWFncTVtZ0pKazN3MmQ4M2RUeVpYV0NwbUYzSCJ9"   
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************.ErNbB9IFW5hNCALa1-Oxhk_rhnHolTnY8gbB68ZI7O0un_weoyoPDk-qVrPTCWW2aKcMhrghiQdSbXjvpn2U9Q"
    plugin_id = "ZJ8e962fd71c8d"
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\通用-报告总结\报告数据第二版.xlsx")
    for index, row in df[40:].iterrows():
        input_param = {
            "userInformation": row["用户画像信息"],
            "report": row["报告内容"],
            "healthLable": row["健康标签"],
            "datatime": row["datatime"] if row["datatime"] else "",
        }
        
        result, time_cost = report_test(input_param, plugin_id, agent_sdk_token)
        print(f"进行到第{index+1}条")

        result = json.loads(result)

        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        df.at[index, "字数"] = len(result.get("data", {}).get("outPut", {}).get("content", ""))
        df.at[index, "API耗时"] = time_cost
        df.at[index, "tokens"] = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\通用-报告总结\报告数据第二版_{timestamp}.xlsx", index=False)
    print(f"workspace\通用-报告总结\报告数据第二版_{timestamp}.xlsx")

def agent_test():
    # 测试环境配置
    test_config = {
        "micro_app_id": 1377,
        "app_key": "53ae2751e36c458cbbadc30a7341bf87",
        "app_secret": "fabdd34adbf04d6e5516100bc63626bd"
    }
    agent_tester = AgentTester(**test_config)
    df = pd.read_excel("workspace\通用-报告总结\报告数据第二版.xlsx")
    for index, row in df.iterrows():
        input_param = {
            "userInformation": row["用户画像信息"],
            "report": row["报告内容"],
            "healthLable": row["健康标签"],
            "datatime": row["datatime"] if row["datatime"] else "",
        }
        result = agent_tester.test_stream(user_param = input_param)
        df.at[index, "首token时间"] = result.get("first_token_time", "")
        df.at[index, "总时间"] = result.get("total_time", "")
        df.at[index, "流式内容"] = result.get("content", "")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\通用-报告总结\报告数据第二版——agent测试_{timestamp}.xlsx", index=False)
    print(f"workspace\通用-报告总结\报告数据第二版——agent测试_{timestamp}.xlsx")

if __name__ == "__main__":
    main()
    # agent_test()


