import numpy as np


def convert_numpy_types(obj):
    """
    将numpy数据类型转换为Python原生类型，以便进行JSON序列化。
    
    该函数递归处理嵌套的数据结构，将numpy的数值类型转换为Python原生类型：
    - numpy.integer -> int
    - numpy.floating -> float
    - numpy.ndarray -> list
    """
    if isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    return obj