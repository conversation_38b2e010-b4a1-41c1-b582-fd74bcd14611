{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-14T11:04:36.539Z", "updatedAt": "2025-07-14T11:04:36.550Z", "resourceCount": 4}, "resources": [{"id": "patent-writing-workflow", "source": "project", "protocol": "execution", "name": "Patent Writing Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/patent-writer/execution/patent-writing-workflow.execution.md", "metadata": {"createdAt": "2025-07-14T11:04:36.544Z", "updatedAt": "2025-07-14T11:04:36.544Z", "scannedAt": "2025-07-14T11:04:36.544Z", "path": "role/patent-writer/execution/patent-writing-workflow.execution.md"}}, {"id": "patent-writer", "source": "project", "protocol": "role", "name": "Patent Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/patent-writer/patent-writer.role.md", "metadata": {"createdAt": "2025-07-14T11:04:36.546Z", "updatedAt": "2025-07-14T11:04:36.546Z", "scannedAt": "2025-07-14T11:04:36.546Z", "path": "role/patent-writer/patent-writer.role.md"}}, {"id": "patent-analysis", "source": "project", "protocol": "thought", "name": "Patent Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/patent-writer/thought/patent-analysis.thought.md", "metadata": {"createdAt": "2025-07-14T11:04:36.549Z", "updatedAt": "2025-07-14T11:04:36.549Z", "scannedAt": "2025-07-14T11:04:36.549Z", "path": "role/patent-writer/thought/patent-analysis.thought.md"}}, {"id": "technical-comprehension", "source": "project", "protocol": "thought", "name": "Technical Comprehension 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/patent-writer/thought/technical-comprehension.thought.md", "metadata": {"createdAt": "2025-07-14T11:04:36.550Z", "updatedAt": "2025-07-14T11:04:36.550Z", "scannedAt": "2025-07-14T11:04:36.550Z", "path": "role/patent-writer/thought/technical-comprehension.thought.md"}}], "stats": {"totalResources": 4, "byProtocol": {"execution": 1, "role": 1, "thought": 2}, "bySource": {"project": 4}}}