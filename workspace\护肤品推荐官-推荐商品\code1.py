import json
def main(params):
  skin_problem = None
  benefit = None
  category = None
  fit_skin = None
  usage_scenario = None
  target_user = None
  product_brand = None
  product_name = None
  price_budget = None
  product_origin = None
  suitable_season = None
  texture_feeling = None
  core_ingredients = None
  unit = "组合"
  top_k = 1
  requirement_text = "用户产品需求："
  
  

  if 'skin_problem' in params and params['skin_problem'] :
    skin_problem =transArray2String(params['skin_problem'])
    requirement_text += f"适合{skin_problem}皮肤问题;"
  
  if 'benefit' in params and params['benefit'] :
    benefit = transArray2String(params['benefit']); 
    requirement_text += f"具有{benefit}的功效;"
    
  if 'category' in params and params['category'] :
    category = trans2String(params['category'])
    requirement_text += f"为{category}类型;"
     # 有传类型，就是查单品
    unit = "单品"
    top_k = 3 
    
  if 'fit_skin' in params and params['fit_skin'] :
    fit_skin = trans2String( params['fit_skin'])
    requirement_text += f"适合{fit_skin}肤质;"
    
  if 'usage_scenario' in params and params['usage_scenario'] :
    usage_scenario = trans2String( params['usage_scenario'])
    requirement_text += f"适合{usage_scenario}使用场景;"
    
  if 'target_user' in params and params['target_user'] :
    target_user = trans2String( params['target_user'])
    requirement_text += f"适合{target_user}用户使用;"
    
  if 'product_brand' in params and params['product_brand'] :
    product_brand = trans2String( params['product_brand'])
    requirement_text += f"是{product_brand}品牌;"
    
  if 'product_name' in params and params['product_name'] :
    product_name = trans2String( params['product_name'])
    requirement_text += f"名称为{product_name};"
    
  if 'price_budget' in params and params['price_budget'] :
    price_budget = trans2String( params['price_budget'])
    requirement_text += f"价格范围在{price_budget};"
    
  if 'product_origin' in params and params['product_origin'] :
    product_origin = trans2String( params['product_origin'])
    requirement_text += f"产地在{product_origin};"
    
  if 'suitable_season' in params and params['suitable_season'] :
    suitable_season = trans2String( params['suitable_season'])
    requirement_text += f"适合{suitable_season}季节使用;"
    
  if 'texture_feeling' in params and params['texture_feeling'] :
    texture_feeling = transArray2String( params['texture_feeling'])
    requirement_text += f"质地肤感为{texture_feeling};"
    
  if 'core_ingredients' in params and params['core_ingredients'] :
    core_ingredients = transArray2String( params['core_ingredients'])
    requirement_text += f"有{core_ingredients}等核心成分;"
    
  
  output_object ={
        "skin_problem": skin_problem,
        "benefit" : benefit,
        "category": category,
        "fit_skin": fit_skin,
        "usage_scenario": usage_scenario,
        "target_user": target_user,
        "product_brand": product_brand,
        "product_name": product_name,
        "price_budget": price_budget,
        "product_origin": product_origin,
        "suitable_season": suitable_season,
        "texture_feeling": texture_feeling,
        "core_ingredients": core_ingredients,
        "unit": unit,
        "top_k": top_k,
        "requirement_text": requirement_text
  }
     
  return output_object
  
def transArray2String(src) :
  
  # 转数组
  value = "0"
  
  if isinstance(src, list) :
    value = ",".join(src)
  else :
    try :
      data = json.loads(src)
      if isinstance(data, list) :
        value = ",".join(data)
      else:
        value = data
    except :
      value = src
  return value
  
def trans2String(src) :
  # 转字符串
  value = None
  data = None
  
  try :
    data = json.loads(src)
  except :
    data = src
  
  if isinstance(data, list) :
    value = data[0]
  else :
    value = data
  return value