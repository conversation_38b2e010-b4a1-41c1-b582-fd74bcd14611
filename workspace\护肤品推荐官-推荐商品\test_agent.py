import pandas as pd
import os
import sys
from datetime import datetime
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
sys.path.append(project_root)
from utils.agent_tester import AgentTester
from tools.count_tokens import count_tokens, get_v3_tokenizer

def main():
    tokenizer = get_v3_tokenizer()
    # bagel_token = os.getenv("BAGEL_TOKEN")
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ3Mzg4OTU0Njc0LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMDBkODU1OTA4NzJlZDNiMWI1IiwianRpIjoiM2Q4MWVlMTAtZTEzOS00Mjk3LWJmYTItYTlhYTk2ODljNDkyIiwiZXhwIjoxNzQ3NDc1MzU0LCJpYXQiOjE3NDczODg5NTQsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.QLSThyGKlqWF89w69K2tBv-RrtSlK6XSM3-P-w-wGUdiJpdHLZXtMuKroRPMhbWuWdup6fb9aHNCVxQf18x_dw"
    is_test_env = True
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1409,
            app_key="53ae2751e36c458cbbadc30a7341bf87",
            app_secret="fabdd34adbf04d6e5516100bc63626bd",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )
    path = "workspace\护肤品推荐官-推荐商品\推荐商品-数据3.0.xlsx"
    path_name = os.path.splitext(path)[0]
    df = pd.read_excel(path)
    new_column_names = ["首token时间", "总时间", "输出"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():
        if not pd.isna(row["category"]):
            category = row["category"]
        else:
            category = ""
        df.at[index, "category"] = category
        
        user_param = {
            "category": category,
            "fit_skin": row["fit_skin"],
            "result_report": row["result_report"],
            "userInfo": row["userInfo"],
            "bagelToken": bagelToken,
        }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "流式输出"] = result["content"]
        df.at[index, "字数"] = len(result["content"])
        token = count_tokens(result["content"], tokenizer=tokenizer)
        df.at[index, "tokens数"] = token
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_path = f"{path_name}_智能体结果{timestamp}.xlsx"
    df.to_excel(new_path, index=False)
    print(f"结果已保存到{new_path}")

if __name__ == "__main__":
    main()
