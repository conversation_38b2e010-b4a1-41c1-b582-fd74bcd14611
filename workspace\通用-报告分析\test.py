# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from utils.apiLLM import ApiLLM
from utils.agent_tester import AgentTester

bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzUwMzI1MTgxNDk2LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMjBjODNmNzYxMWFkOWUwMDNlIiwianRpIjoiZjY5OTU2MzQtMzhjYy00MjNiLWEwZGQtMGFjZDVlNjVlYzQ4IiwiZXhwIjoxNzUwNDExNTgxLCJpYXQiOjE3NTAzMjUxODEsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.YeedANL0uL6Dn236bvZujFtooeYxZjAJT_ir7ZhxkltDmXktgo_RmXGypn9jWEdlukuscCcgZxAvVLSnFpzGdQ"
def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVQ0dsdWRpYUdqa1JpdUwrNlU0eFhwUWtEOGdwb09QaUtNRWUzSTdWNFkxNGc9PSJ9"

    plugin_id = "ZJa44ef3fa261e"
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\通用-报告分析\报告分析数据.xlsx")
    
    # 初始化所需的列
    required_columns = ["回答", "字数", "API耗时", "tokens"]
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""

    for index, row in df.iterrows():
        try:
            print(f"处理第 {index} 行")
            
            # 处理history
            if pd.isna(row["history"]):
                history = []
            else:
                try:
                    # 首先尝试直接解析
                    history = json.loads(row["history"])
                except json.JSONDecodeError:
                    try:
                        # 如果直接解析失败，尝试清理和修复JSON字符串
                        history_str = row["history"]
                        # 确保字符串以 [ 开始，以 ] 结束
                        if not history_str.strip().startswith('['):
                            history_str = '[' + history_str
                        if not history_str.strip().endswith(']'):
                            history_str = history_str + ']'
                        # 修复可能的格式问题
                        history_str = history_str.replace('}, {', '},{')  # 移除多余的空格
                        history_str = history_str.replace('} ,{', '},{')  # 修复逗号前的空格
                        history_str = history_str.replace('}, {', '},{')  # 修复逗号后的空格
                        
                        history = json.loads(history_str)
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {str(e)}")
                        print(f"问题数据: {row['history']}")
                        history = []  # 如果所有尝试都失败，使用空数组
            
            input_param = {
                "userInfo": row["userInfo"],
                "history": history,
                "userReport": row["userReport"],
                "query": row["query"],
                "expire": row["expire"],
                "type": row["type"]
            }
            print(f"请求参数： {input_param}")

            result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)
            
            # 添加错误处理
            if result is None:
                print(f"API返回空结果，跳过第 {index} 行")
                continue
                
            try:
                result_json = json.loads(result)
                if result_json.get("code") != 0:  # 假设0是成功状态码
                    print(f"API返回错误: {result_json.get('msg')}")
                    continue
                    
                df.at[index, "回答"] = result_json.get("data", {}).get("outPut", {}).get("content", "")
                df.at[index, "字数"] = len(result_json.get("data", {}).get("outPut", {}).get("content", ""))
                print(f"字数{index}：{len(result_json.get('data', {}).get('outPut', {}).get('content', ''))}")
                df.at[index, "API耗时"] = time_cost
                token = count_tokens(result_json.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)
                print(f"tokens数{index}：{token}")
                df.at[index, "tokens"] = token
            except Exception as e:
                print(f"处理API结果时发生错误: {str(e)}")
                continue

        except Exception as e:
            print(f"处理第 {index} 行时发生错误: {str(e)}")
            continue

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\通用-报告分析\报告分析数据.xlsx_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\通用-报告分析\报告分析数据.xlsx_{timestamp}.xlsx")

def test_agent():
    tokenizer = get_v3_tokenizer()
    is_test_env = True
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1549,
            app_key="53ae2751e36c458cbbadc30a7341bf87",
            app_secret="fabdd34adbf04d6e5516100bc63626bd",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )
    
    path = "workspace\通用-报告分析\报告分析数据.xlsx"
    path_name = os.path.splitext(path)[0]
    df = pd.read_excel(path)
    
    new_column_names = ["首token时间", "总时间", "回答", "字数", "tokens数"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():
        # 处理history
        if pd.isna(row["history"]):
            history = []
        else:
            try:
                # 首先尝试直接解析
                history = json.loads(row["history"])
            except json.JSONDecodeError:
                try:
                    # 如果直接解析失败，尝试清理和修复JSON字符串
                    history_str = row["history"]
                    # 确保字符串以 [ 开始，以 ] 结束
                    if not history_str.strip().startswith('['):
                        history_str = '[' + history_str
                    if not history_str.strip().endswith(']'):
                        history_str = history_str + ']'
                    # 修复可能的格式问题
                    history_str = history_str.replace('}, {', '},{')  # 移除多余的空格
                    history_str = history_str.replace('} ,{', '},{')  # 修复逗号前的空格
                    history_str = history_str.replace('}, {', '},{')  # 修复逗号后的空格
                    
                    history = json.loads(history_str)
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {str(e)}")
                    print(f"问题数据: {row['history']}")
                    history = []  # 如果所有尝试都失败，使用空数组
        
        user_param = {
                "userInfo": row["userInfo"],
                "history": history,
                "userReport": row["userReport"],
                "query": row["query"],
                "expire": row["expire"],
                "type": row["type"]
            }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "回答"] = result["content"]
        df.at[index, "字数"] = len(result["content"])
        token = count_tokens(result["content"], tokenizer=tokenizer)
        df.at[index, "tokens数"] = token
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_path = f"{path_name}_智能体结果{timestamp}.xlsx"
    df.to_excel(new_path, index=False)
    print(f"结果已保存到{new_path}")    

if __name__ == "__main__":
    main()
    # test_agent()





