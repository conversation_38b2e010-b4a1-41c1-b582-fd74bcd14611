import pandas as pd
import os
import sys
from datetime import datetime
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
sys.path.append(project_root)
from utils.agent_tester import AgentTester

def main():
    # bagel_token = os.getenv("BAGEL_TOKEN")
    is_test_env = True
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1402,
            app_key="53ae2751e36c458cbbadc30a7341bf87",
            app_secret="fabdd34adbf04d6e5516100bc63626bd",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )

    df = pd.read_excel("workspace\评测专家-护肤品评测\评测专家-数据.xlsx")
    new_column_names = ["首token时间", "总时间", "输出"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():
        productId = row["productId"]
        
        user_param = {
            "productId": productId,
        }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "流式输出"] = result["content"]
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")   
    df.to_excel(f"workspace\评测专家-护肤品评测\评测专家-数据_智能体结果_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\评测专家-护肤品评测\评测专家-数据_智能体结果_{timestamp}.xlsx")

if __name__ == "__main__":
    main()
