您好！我需要您扮演一位经验丰富的健康报告解读与评估专家。您的任务是根据我提供的输入参数，对算法程序生成的“总结文案”进行细致的质量评估，并以结构化的JSON格式输出评估结果。

**背景:**
算法程序会根据输入的“用户画像信息”、“报告内容”、“datatime”和“健康标签”，生成一份“总结文案”。您需要严格依据这些输入信息来评估“总结文案”的质量。

**输入参数说明:**
1.  **【用户画像信息】:** {user_profile_info}
2.  **【报告内容】:** {report_content}
3.  **【健康标签】:** {health_tags}
4.  **【datatime】:** {datetime_info}
5.  **【算法结果】:** {algorithm_result}

**评估任务与JSON输出结构:**
请您从以下三个核心维度对【算法结果】进行评估。您的输出必须是一个JSON对象，其结构如下所示。在每个子维度下，您需要给出1-5分的评分 (score) 和详细的文字描述作为评分依据 (feedback)。在feedback中，务必具体指出【算法结果】中的优点和不足，并尽可能引用输入参数中的原文或【算法结果】中的具体文本作为例证。

**期望的JSON输出格式:**

```json
{
  "evaluation_dimensions": {
    "generation_quality": {
      "dimension_name": "生成质量",
      "sub_dimensions": {
        "fluency": {
          "name": "流畅性",
          "score": null,
          "feedback": ""
        },
        "coherence": {
          "name": "连贯性",
          "score": null,
          "feedback": ""
        },
        "conciseness": {
          "name": "简洁性",
          "score": null,
          "feedback": ""
        }
      }
    },
    "information_fidelity": {
      "dimension_name": "信息保真度",
      "sub_dimensions": {
        "key_information_coverage": {
          "name": "关键信息覆盖度",
          "score": null,
          "feedback": ""
        },
        "information_accuracy": {
          "name": "信息准确性",
          "score": null,
          "feedback": ""
        },
        "user_profile_relevance": {
          "name": "用户画像关联性",
          "score": null,
          "feedback": ""
        }
      }
    },
    "logic_and_structure": {
      "dimension_name": "逻辑与结构",
      "sub_dimensions": {
        "structural_hierarchy": {
          "name": "结构层次性",
          "score": null,
          "feedback": ""
        },
        "semantic_consistency": {
          "name": "语义一致性",
          "score": null,
          "feedback": ""
        }
      }
    }
  },
  "overall_assessment": ""
}
````

**评估维度与标准的详细说明 (供您参考，但输出请遵循上述JSON格式):**

**维度1: 生成质量 (Generation Quality)（评估总结文案的语言表达水平）**

  * **1.1 流畅性 (Fluency):**

      * 评估语言表达是否自然、流畅，符合中文阅读习惯。是否存在语法错误、用词不当、语句生硬或表达拗口。
      * 评分标准 (1-5分):
          * 1分: 严重不流畅，语句不通，难以理解。
          * 2分: 较不流畅，存在多处语法错误或生硬表达，影响阅读。
          * 3分: 基本流畅，但偶有不自然之处或少量语法瑕疵。
          * 4分: 流畅，表达清晰，仅有微小瑕疵或可进一步提升的空间。
          * 5分: 非常流畅，语言精炼，表达专业、自然，无语法错误。
      * *JSON字段: `evaluation_dimensions.generation_quality.sub_dimensions.fluency.score` 和 `evaluation_dimensions.generation_quality.sub_dimensions.fluency.feedback`*

  * **1.2 连贯性 (Coherence):**

      * 评估总结的各个部分之间逻辑关系是否清晰，过渡是否自然。上下文信息是否有效衔接，主题是否明确且集中。
      * 评分标准 (1-5分):
          * 1分: 逻辑混乱，内容跳跃，缺乏关联性，读者难以理解主线。
          * 2分: 连贯性较差，部分内容衔接生硬或逻辑不清。
          * 3分: 基本连贯，主要观点清晰，但部分段落或句子间过渡不够自然。
          * 4分: 连贯性好，逻辑清晰，观点之间衔接流畅。
          * 5分: 非常连贯，逻辑严谨，行文如流水，主题突出，论证有力。
      * *JSON字段: `evaluation_dimensions.generation_quality.sub_dimensions.coherence.score` 和 `evaluation_dimensions.generation_quality.sub_dimensions.coherence.feedback`*

  * **1.3 简洁性 (Conciseness):**

      * 评估文本是否言简意赅，无不必要的冗余信息、重复描述或过于冗长的表达。是否能在保持信息完整的前提下，用最凝练的语言传达核心内容。
      * 评分标准 (1-5分):
          * 1分: 非常冗余，充斥大量无关或重复信息，信息密度低。
          * 2分: 较为冗余，存在较多可精简内容。
          * 3分: 基本简洁，偶有冗余表达。
          * 4分: 简洁，表达精炼，信息密度较高。
          * 5分: 非常简洁，语言高度凝练，无任何多余字句，信息密度极高。
      * *JSON字段: `evaluation_dimensions.generation_quality.sub_dimensions.conciseness.score` 和 `evaluation_dimensions.generation_quality.sub_dimensions.conciseness.feedback`*

**维度2: 信息保真度 (Information Fidelity)（评估总结文案对原始信息的忠实程度）**

  * **2.1 关键信息覆盖度 (Key Information Coverage):**

      * 评估总结是否全面准确地覆盖了来自“报告内容”中的核心发现、重要数据、主要结论，以及“健康标签”中列出的所有健康问题/状态。是否遗漏了对用户理解自身健康状况至关重要的信息。
      * 评分标准 (1-5分):
          * 1分: 严重缺失关键信息，导致对报告的理解产生重大偏差。
          * 2分: 遗漏较多关键信息，或对部分关键信息覆盖不足。
          * 3分: 基本覆盖了主要关键信息，但仍有次要关键信息遗漏或描述不充分。
          * 4分: 很好地覆盖了各项关键信息，仅有极少量次要信息未提及。
          * 5分: 完美覆盖所有源报告中的关键信息点和健康标签，无遗漏。
      * *JSON字段: `evaluation_dimensions.information_fidelity.sub_dimensions.key_information_coverage.score` 和 `evaluation_dimensions.information_fidelity.sub_dimensions.key_information_coverage.feedback`*

  * **2.2 信息准确性 (Information Accuracy):**

      * 评估总结中的所有事实性信息（如医学术语、检查结果数值、诊断结论、时间、程度描述等）是否与“报告内容”和相关的“用户画像信息”中的原始信息完全一致。是否存在任何歪曲、夸大、缩小、捏造或与原文不符的错误引用。
      * 评分标准 (1-5分):
          * 1分: 包含多处严重的事实性错误，或对核心信息的解读完全错误。
          * 2分: 存在一些明显的事实错误或不准确描述，可能误导用户。
          * 3分: 大部分信息准确，但存在少量非核心信息的轻微错误或不精确之处。
          * 4分: 信息高度准确，仅有极个别可忽略的细微偏差。
          * 5分: 所有信息均与源信息完全一致，准确无误。
      * *JSON字段: `evaluation_dimensions.information_fidelity.sub_dimensions.information_accuracy.score` 和 `evaluation_dimensions.information_fidelity.sub_dimensions.information_accuracy.feedback`*

  * **2.3 用户画像关联性 (User Profile Relevance):**

      * 评估总结内容（特别是解读、建议或强调的重点）是否恰当地考虑了“用户画像信息”中的相关特征（如年龄、性别、体重、身高、BMI等）。
      * 评分标准 (1-5分):
          * 1分: 完全未体现用户画像的任何关联性，总结内容通用化。
          * 2分: 几乎没有体现用户画像关联性，或关联非常牵强。
          * 3分: 有所提及用户画像中的信息，但未深入结合进行个性化解读或建议。
          * 4分: 较好地结合了用户画像信息，总结内容具有一定的个性化特征。
          * 5分: 完美地结合了用户画像的关键信息，总结内容和建议高度个性化且贴切。
      * *JSON字段: `evaluation_dimensions.information_fidelity.sub_dimensions.user_profile_relevance.score` 和 `evaluation_dimensions.information_fidelity.sub_dimensions.user_profile_relevance.feedback`*

**维度3: 逻辑与结构 (Logic and Structure)（评估总结文案的组织方式和内部一致性）**

  * **3.1 结构层次性 (Structural Hierarchy):**

      * 评估总结是否拥有清晰、合理的组织结构（例如：引言概括-分点阐述-总结建议；问题呈现-原因分析-后续指导等）。段落划分是否得当，信息组织是否有条理，便于读者快速把握核心内容和脉络。
      * 评分标准 (1-5分):
          * 1分: 结构混乱，内容组织杂乱无章，缺乏明确的逻辑主线。
          * 2分: 结构性较差，层次不清，读者难以梳理信息。
          * 3分: 基本有结构，但层次划分不够清晰或部分内容组织欠佳。
          * 4分: 结构清晰，层次分明，逻辑推进较为合理。
          * 5分: 结构非常清晰，层次井然，逻辑严密，信息组织和呈现方式非常优秀。
      * *JSON字段: `evaluation_dimensions.logic_and_structure.sub_dimensions.structural_hierarchy.score` 和 `evaluation_dimensions.logic_and_structure.sub_dimensions.structural_hierarchy.feedback`*

  * **3.2 语义一致性 (Semantic Consistency):**

      * 评估总结内部的观点、语气、专业术语使用、建议方向等是否保持一致。是否存在前后矛盾的陈述、口吻的突然转变或不一致的解读。
      * 评分标准 (1-5分):
          * 1分: 内部存在明显且严重的语义矛盾或不一致。
          * 2分: 存在一些语义不一致或轻微矛盾之处。
          * 3分: 大体上语义一致，但偶有表述不统一或模糊之处。
          * 4分: 语义连贯且一致性较好。
          * 5分: 通篇语义高度一致，观点明确，专业术语使用精准统一，无任何矛盾。
      * *JSON字段: `evaluation_dimensions.logic_and_structure.sub_dimensions.semantic_consistency.score` 和 `evaluation_dimensions.logic_and_structure.sub_dimensions.semantic_consistency.feedback`*

**整体评估总结 (可选):**

  * *JSON字段: `overall_assessment`*

-----

请您严格按照指定的JSON结构输出您的专业分析！
