# -*- coding: utf-8 -*-
import sys
import os
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, project_root)
import time
import json
import requests
from typing import Optional, Literal, List
from clife_svc.libs.log import klogger
from tenacity import retry, wait_random_exponential, stop_after_attempt
from pydantic import BaseModel

# OpenAi Compatible API


class FunctionCallResponse(BaseModel):
    name: Optional[str] = None
    arguments: Optional[str] = None


class ChatMessage(BaseModel):
    role: Literal["user", "assistant", "system", "function"]
    content: str = None
    reasoningContent: str = None
    name: Optional[str] = None
    function_call: Optional[FunctionCallResponse] = None


class ChatCompletionResponseChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: Literal["stop", "length", "function_call"]


class ChatCompletionResponse(BaseModel):
    choices: List[ChatCompletionResponseChoice]


class ApiLLM:
    """ API LLM """
    def __init__(self,
                 model,
                 temperature,
                 completion_url,
                 stream_url,
                 app_id=None, #'00000012'
                 micro_app_id=None,
                 micro_app_test=None
                 ):
        self.model = model
        self.temperature = float(temperature)
        self.completion_url = completion_url
        self.stream_url = stream_url
        self.app_id = app_id
        self.micro_app_id = micro_app_id
        self.micro_app_test = micro_app_test

    @retry(wait=wait_random_exponential(multiplier=1, max=40), stop=stop_after_attempt(3))
    def __call__(self, messages, stream=False, functions=None, temperature=None):
        start_time = time.time()
        kwargs = {
            'messages': messages,
            'temperature': temperature if temperature else self.temperature,
            'modelName': self.model
        }
        if self.app_id:
            kwargs['appId'] = self.app_id
        if self.micro_app_id:
            kwargs['microAppId'] = self.micro_app_id
        if self.micro_app_test is not None:
            kwargs['microAppTest'] = self.micro_app_test
        # kwargs['microAppTest'] = True   # 默认为测试环境,不计费
        
        if functions:
            # 根据function格式来调整，以接口文档为准
            kwargs['functions'] = [func['function'] for func in functions]

        klogger.info("llm params: {0}".format(kwargs))

        try:
            if 'clife-decision-module' not in self.completion_url:
                headers = {'content-type': 'application/json',
                           'Host': 'clife-decision-module.decision-itest.coding.clife.net'
                           }
            else:
                headers = {'content-type': 'application/json'}
            if stream:
                response = requests.request("POST", self.stream_url, headers=headers,
                                            json=kwargs, stream=True, verify=False)
                return response
            else:
                response = requests.request("POST", self.completion_url, json=kwargs, headers=headers)
                # print(f"Response text: {response.text}")
                response = json.loads(response.text)

                function_call = None
                finish_reason = response["data"][0]["finishReason"]
                if functions and response["data"][0]["finishReason"] == "tool_calls":
                    function_call = FunctionCallResponse(
                        name=response["data"][0]["functionCall"]["name"],
                        arguments=response["data"][0]["functionCall"]["arguments"]
                    )
                    finish_reason = "function_call"
                
                if response["data"][0]["reasoningContent"]:
                    reasoningContent = response["data"][0]["reasoningContent"]
                else:
                    reasoningContent = ''

                message = ChatMessage(
                    role="assistant",
                    content=response["data"][0]["content"],
                    reasoningContent=reasoningContent,
                    function_call=function_call if isinstance(function_call, FunctionCallResponse) else None,
                )
                choice_data = ChatCompletionResponseChoice(
                    index=0,
                    message=message,
                    finish_reason=finish_reason,
                )
                return ChatCompletionResponse(choices=[choice_data])
        except Exception as e:
            klogger.exception("Unable to generate ChatCompletion response")
            klogger.error(f"Exception: {e}")
        finally:
            klogger.info(f"{self.model} response duration: {round(time.time() - start_time, 2)}s")

class ApiMutiLLM:
    """ API LLM 多模态，流式输出处理成非流式"""

    def __init__(self,
                 model,
                 temperature,
                 completion_url,
                 stream_url,
                 app_id=None,
                 micro_app_id=None,
                 micro_app_test=None
                 ):
        self.model = model
        self.temperature = float(temperature)
        self.completion_url = completion_url
        self.stream_url = stream_url
        self.app_id = app_id
        self.micro_app_id = micro_app_id
        self.micro_app_test = micro_app_test

    @retry(wait=wait_random_exponential(multiplier=1, max=40), stop=stop_after_attempt(3))
    def __call__(self, messages, stream=False, functions=None, temperature=None, imageUrl=None):
        kwargs = {
            # 'messages': messages,
            'prompt': messages,
            # 'temperature': temperature if temperature else self.temperature,
            # 'modelName': self.model,
        }
        if self.app_id:
            kwargs['appId'] = self.app_id
        if self.micro_app_id:
            kwargs['microAppId'] = self.micro_app_id
        if self.micro_app_test is not None:
            kwargs['microAppTest'] = self.micro_app_test
        if imageUrl:
            kwargs['imageUrl'] = imageUrl
        if functions:
            # 根据function格式来调整，以接口文档为准
            kwargs['functions'] = [func['function'] for func in functions]

        try:
            headers = {'content-type': 'application/json',
                       'Host': 'clife-decision-module.decision-itest.coding.clife.net'
                       }
            if stream:
                response = requests.request("POST", self.stream_url, headers=headers,
                                            json=kwargs, stream=True, verify=False)
                return response
            else:
                response = requests.request("POST", self.completion_url, json=kwargs, headers=headers)
                # print(f"Response text: {response.text}")
                
                # 检查是否是流式响应格式（以'data: '开头的行）
                if isinstance(response.text, str) and response.text.strip().startswith('data: '):
                    # 处理流式响应
                    full_content = ""
                    function_call = None
                    finish_reason = None
                    
                    # 按行分割响应
                    lines = response.text.strip().split('\n')
                    for line in lines:
                        if line.startswith('data: '):
                            try:
                                # 去掉'data: '前缀并解析JSON
                                data_json = json.loads(line[6:])
                                if 'data' in data_json and 'content' in data_json['data']:
                                    content_chunk = data_json['data']['content']
                                    full_content += content_chunk
                                    
                                    # 检查是否有finishReason
                                    if 'finishReason' in data_json['data'] and data_json['data']['finishReason']:
                                        finish_reason = data_json['data']['finishReason']
                                        
                                    # 检查是否有functionCall
                                    if 'functionCall' in data_json['data'] and data_json['data']['functionCall']:
                                        function_call = FunctionCallResponse(
                                            name=data_json['data']['functionCall'].get('name'),
                                            arguments=data_json['data']['functionCall'].get('arguments')
                                        )
                            except json.JSONDecodeError:
                                continue
                    
                    # 创建完整的响应对象
                    message = ChatMessage(
                        role="assistant",
                        content=full_content,
                        function_call=function_call if isinstance(function_call, FunctionCallResponse) else None,
                    )
                    choice_data = ChatCompletionResponseChoice(
                        index=0,
                        message=message,
                        finish_reason=finish_reason,
                    )
                    return ChatCompletionResponse(choices=[choice_data])
                else:
                    # 处理非流式响应
                    try:
                        response = json.loads(response.text)
                        
                        # 检查是否是流式响应格式
                        if isinstance(response, dict) and "data" in response and isinstance(response["data"], list):
                            # 处理非流式响应
                            function_call = None
                            finish_reason = response["data"][0]["finishReason"]
                            if functions and response["data"][0]["finishReason"] == "tool_calls":
                                function_call = FunctionCallResponse(
                                    name=response["data"][0]["functionCall"]["name"],
                                    arguments=response["data"][0]["functionCall"]["arguments"]
                                )
                                finish_reason = "function_call"
            
                            message = ChatMessage(
                                role="assistant",
                                content=response["data"][0]["content"],
                                function_call=function_call if isinstance(function_call, FunctionCallResponse) else None,
                            )
                            choice_data = ChatCompletionResponseChoice(
                                index=0,
                                message=message,
                                finish_reason=finish_reason,
                            )
                            return ChatCompletionResponse(choices=[choice_data])
                        else:
                            # 处理其他格式的响应
                            function_call = None
                            finish_reason = response.get("finishReason")
                            if functions and response.get("finishReason") == "tool_calls":
                                function_call = FunctionCallResponse(
                                    name=response.get("functionCall", {}).get("name"),
                                    arguments=response.get("functionCall", {}).get("arguments")
                                )
                                finish_reason = "function_call"
            
                            message = ChatMessage(
                                role="assistant",
                                content=response.get("content", ""),
                                function_call=function_call if isinstance(function_call, FunctionCallResponse) else None,
                            )
                            choice_data = ChatCompletionResponseChoice(
                                index=0,
                                message=message,
                                finish_reason=finish_reason,
                            )
                            return ChatCompletionResponse(choices=[choice_data])
                    except json.JSONDecodeError as e:
                        print(f"调用api失败1：{e}")
                        message = ChatMessage(role="assistant", content="API调用出错: " + str(e))
                        choice_data = ChatCompletionResponseChoice(index=0, message=message, finish_reason="error")
                        return ChatCompletionResponse(choices=[choice_data])
        except Exception as e:
            print("调用api失败2：",e)
            # 返回一个错误响应对象而不是None
            message = ChatMessage(role="assistant", content="API调用出错: " + str(e))
            choice_data = ChatCompletionResponseChoice(index=0, message=message, finish_reason="error")
            return ChatCompletionResponse(choices=[choice_data])

def v3_llm():
    v3_llm = ApiLLM(
        model="doubao-deepseek-v3",
        completion_url="http://10.6.14.2:30000/module/byte/getChatCompletions",
        stream_url="http://10.6.14.2:30000/module/byte/getChatCompletionsStream",
        temperature=0.7,
        app_id='00000034'
    )
    return v3_llm

def gpt4o_llm():
    gpt_llm = ApiLLM(
        model="gpt-4o",
        completion_url="http://10.6.14.2:30000/module/gpt/getChatCompletions",
        stream_url="http://10.6.14.2:30000/module/gpt/getChatCompletionsStream",
        temperature=0.1,
        app_id='00000034'
    )
    return gpt_llm

def qwen_llm():
    qwen_llm = ApiLLM(
        model="qwen-max",
        completion_url="http://10.6.14.2:30000/module/alibaba/getChatCompletions",
        stream_url="http://10.6.14.2:30000/module/alibaba/getChatCompletionsStream",
        temperature=0.1,
        app_id='00000034'
    )
    return qwen_llm

if __name__ == '__main__':
    dsllm = ApiLLM(
                model="doubao-deepseek-v3",
                completion_url="http://10.6.14.2:30000/module/byte/getChatCompletions", 
                stream_url="http://10.6.14.2:30000/module/byte/getChatCompletionsStream",
                temperature=0.3,
                app_id='00000034',
                # micro_app_id=199,
                # micro_app_test=True
                )
    messages = [{"role": "user", "content": "怎么保护自己"}]
    response = dsllm(messages=messages, stream=False)
    print(response.choices[0].message.content)
    print("===========================")
    print(response.choices[0].message.reasoningContent)