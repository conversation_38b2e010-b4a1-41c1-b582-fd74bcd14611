import pandas as pd
import os
import sys
from datetime import datetime
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
sys.path.append(project_root)
from utils.agent_tester import AgentTester

def main():
    bagel_token = os.getenv("BAGEL_TOKEN")
    tester = AgentTester(
        micro_app_id=1279,
        app_key="53ae2751e36c458cbbadc30a7341bf87",
        app_secret="fabdd34adbf04d6e5516100bc63626bd",
        is_test_env=True
    )

    df = pd.read_excel("个性化_test/个性化数据集_结果_20250422_171917.xlsx")
    new_column_names = ["首token时间", "总时间", "流式输出"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df[:1].iterrows():
        chat = row["输入"]
        history = []
        businessDomain = row["businessDomain"]
        app_name = row["智能体类型"]
        
        user_param = {
            "chat": chat,
            "history": history,
            "businessDomain": businessDomain,
            "bagelToken": bagel_token,
            "app_name": app_name
        }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "流式输出"] = result["content"]
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")   
    df.to_excel(f"个性化_test/个性化数据集_智能体结果_{timestamp}.xlsx", index=False)
    print(f"结果已保存到个性化_test/个性化数据集_智能体结果_{timestamp}.xlsx")

if __name__ == "__main__":
    main()
