# 获取token pip install pycryptodome
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import json
from datetime import datetime, timedelta
import time
import requests

app_key = "53ae2751e36c458cbbadc30a7341bf87"
app_secret = "fabdd34adbf04d6e5516100bc63626bd"
user_info = {
    "userUuid": "123",
    "appSecret": app_secret,
    "expireTime": 1719196220850
}
def get_key(secret):
    return secret.encode('utf-8')

def encrypt(plain_text, secret):
    key = get_key(secret)
    iv = b'\0' * 16  # 使用全零的初始化向量（IV），这在实际应用中应避免
    cipher = Cipher(algorithms.AES(key), mode=modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()

    # PKCS7 padding
    block_size = 16
    pad_length = block_size - len(plain_text) % block_size
    padded_data = plain_text + chr(pad_length) * pad_length
    encrypted_bytes = encryptor.update(padded_data.encode('utf-8')) + encryptor.finalize()
    return base64.b64encode(encrypted_bytes).decode('utf-8')
def get_token(app_id,app_key,app_secret):
    app_key = app_key
    app_secret = app_secret
    app_id = app_id
    expire_time = int((datetime.now() + timedelta(days=2)).timestamp() * 1000)
    user_info = {
        "userUuid": "quick_note_123",
        "appSecret": app_secret,
        "expireTime": expire_time
    }
    encrypt_user_info = encrypt(json.dumps(user_info, separators=(',', ':')), app_key)
    token = {
        "appId": app_id,
        "token": encrypt_user_info
    }
    authorization = base64.b64encode(json.dumps(token, separators=(',', ':')).encode('utf-8')).decode('utf-8')
    return authorization

print(get_token("00000034",app_key=app_key,app_secret=app_secret))