# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from utils.apiLLM import ApiLLM
from utils.agent_tester import AgentTester

def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVRGoyVnFKbW5CckJzK25pZ3Z6dEJZRFZPOXlzR21LUGxGNDdnYXR5ZWUyTWc9PSJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ3NjQ1NTI3MzgyLCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMDBkODU1OTA4NzJlZDNiMWI1IiwianRpIjoiNzU2M2QzMzItNGZkYy00NTliLThjYjYtOGE5MWIzM2M4MWU5IiwiZXhwIjoxNzQ3NzMxOTI3LCJpYXQiOjE3NDc2NDU1MjcsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.dtWuOb6lnt2v8gUnyxcXmaABb0Oo49-BAGr-8u50vTAnn-dcmGOxHF9C-qANydtPJV1ipYdvqlUJyeiwzKTTLQ"
    plugin_id = "ZJ4caa80a0c96b" # ZJ39b5111665af  原ZJ4caa80a0c96b
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\护肤品PK\护肤品PK数据.xlsx")
    
    # 初始化所需的列
    required_columns = ["产品信息", "回答", "字数", "API耗时", "tokens"]
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""

    for index, row in df.iterrows():
        if not pd.isna(row["compareProductId"]):
            input_param = {
                "productId": int(row["productId"]),
                "compareProductId": int(row["compareProductId"]),
            }
        else:
            input_param = {
                "productId": int(row["productId"]),
            }
        
        result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)
        
        result = json.loads(result)
        df.at[index, "产品信息"] = result.get("data", {}).get("outPut", {}).get("productDetailList", "")
        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        # print(f"回答{index}：{result.get('data', {}).get('outPut', {}).get('content', '')}")
        df.at[index, "字数"] = len(result.get("data", {}).get("outPut", {}).get("content", ""))
        print(f"字数{index}：{len(result.get('data', {}).get('outPut', {}).get('content', ''))}")
        df.at[index, "API耗时"] = time_cost
        token = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)
        print(f"tokens数{index}：{token}")
        df.at[index, "tokens"] = token

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\护肤品PK\护肤品PK数据_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\护肤品PK\护肤品PK数据_{timestamp}.xlsx")

def test_agent():
    tokenizer = get_v3_tokenizer()
    is_test_env = True
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1448,
            app_key="53ae2751e36c458cbbadc30a7341bf87",
            app_secret="fabdd34adbf04d6e5516100bc63626bd",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )
    path = "workspace\护肤品PK\护肤品PK数据.xlsx"
    path_name = os.path.splitext(path)[0]
    df = pd.read_excel(path)
    new_column_names = ["首token时间", "总时间", "流式输出"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():
        user_param = {
            "productId": int(row["productId"]),
            "compareProductId": int(row["compareProductId"]),
        }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "流式输出"] = result["content"]
        df.at[index, "字数"] = len(result["content"])
        token = count_tokens(result["content"], tokenizer=tokenizer)
        df.at[index, "tokens数"] = token
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_path = f"{path_name}_智能体结果{timestamp}.xlsx"
    df.to_excel(new_path, index=False)
    print(f"结果已保存到{new_path}")    

if __name__ == "__main__":
    # main()
    test_agent()


