# -*- coding: utf-8 -*-
import requests
import time
import os
import json

class PluginTester:
    def __init__(self, base_url: str = "https://itest.clife.net/agent-sdk/v1/microApp/exePlugin",
                 agent_sdk_token: str = None,
                 test_mode: bool = True):
        """     
        Args:
            base_url: API基础URL
            agent_sdk_token: 认证token
        """
        if test_mode:
            self.base_url = "https://itest.clife.net/agent-sdk/v1/microApp/exePlugin"
        else:
            self.base_url = "https://cms.clife.net/agent-sdk/v1/microApp/exePlugin"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": agent_sdk_token
        }

    def execute_plugin(self, plugin_id: str, input_param: dict) -> tuple[str, float]:
        """
        执行插件调用
        
        Args:
            plugin_id: 插件ID
            input_param: 输入参数
            
        Returns:
            Dict: API响应结果
            time_cost: 请求耗时
        """
        data = {
            "pluginId": plugin_id,
            "inputParam": input_param
        }
        start_time = time.time()
        try:
            json_data = json.dumps(data, ensure_ascii=False)
            response = requests.post(self.base_url, headers=self.headers, data=json_data)
            end_time = time.time()
            time_cost = end_time - start_time
            print(f"API结果：{response.json()}, 耗时：{time_cost}")
            result = json.dumps(response.json(), ensure_ascii=False)
            return result, time_cost
        except Exception as e:
            end_time = time.time()
            time_cost = end_time - start_time
            error_response = {"code": -1, "message": f"API请求出错: {str(e)}"}
            print(f"API结果：{error_response}, 耗时：{time_cost}")
            result = json.dumps(error_response, ensure_ascii=False)
            return result, time_cost



if __name__ == "__main__":
    # 创建客户端实例
    agent_sdk_token = os.getenv("AGENT_SDK_TOKEN")
    print(agent_sdk_token)
    bagel_token = os.getenv("BAGEL_TOKEN")
    client = PluginTester(agent_sdk_token=agent_sdk_token)
    
    # 调用示例
    result = client.execute_plugin(
        plugin_id="ZJb1ec0b82d7fc",
        input_param = {
            "chat": "你好",
            "history": [],
            "themeId": "皮肤健康",
            "bagelToken": bagel_token,
            "micro_app_id": '794',
            "businessDomain": "皮肤健康",
            "emotional_type": '快乐',
            "emotional_strength": '低',
            "app_name": "幼儿生长发育专家",
        }
    )
    
    print(result)

