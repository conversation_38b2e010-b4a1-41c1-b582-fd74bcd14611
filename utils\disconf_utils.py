import os
from typing import Union

import requests
from clife_svc.config.disconf import Disconf as _Disconf
from clife_svc.config.disconf import _ENVIRONMENT
from clife_svc.config.disconf import clogger


class Disconf(_Disconf):
    def __init__(self, keys: Union[str, list]):
        self.__app = 'clife-ai',
        self.__version = '0.0.1-SNAPSHOT'
        self.__configs = self._split2list(keys)

        __ENV = int(os.environ.get("ENVIRONMENT", -1))

        self.__disconf_url = _ENVIRONMENT.get(__ENV).get('disconf_url')
        self.__env_name = _ENVIRONMENT.get(__ENV).get('env_name')

        self.__sess = requests.session()
        self.__conf = self.get_disconf()

    def get_disconf(self) -> dict:
        """
        获取当前项目下的所有配置数据字典
        :return:
        """
        items = {}
        try:
            for conf_key in self.__configs:
                clogger.info(f'Start downloading disconf file {conf_key}')
                item = self._download_file(self.__app, conf_key)
                if item:
                    items.update(item)
            self.__sess.close()

            if not items:
                clogger.error('Error download disconf from {}.'.format(self.__disconf_url))
            else:
                clogger.info('Download disconf files finished.')

            for key in items.keys():
                if '\\u' in items[key]:
                    items[key] = items[key].encode().decode("unicode_escape")
                clogger.info('disconf_item: {}={}'.format(key, repr(items[key]) if '\n' in items[key] else items[key]))
        except Exception as e:
            clogger.error('Error download disconf:{}'.format(e))
        finally:
            return items

    def __getitem__(self, key):
        return self.__conf[key]

    def get(self, key, default=None):
        return self.__conf.get(key, default)

    def get_all_conf(self) -> dict:
        return self.__conf

