# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer

def data_process():
    df = pd.read_excel("护肤品评测-推荐商品\评测-推荐商品-数据.xlsx")
    for index, row in df.iterrows():
        result = row["result"]
        result = json.loads(result)
        df.at[index, "category"] = random.choice(["洁面","化妆水","面部精华","乳液/面霜","眼部护理","面膜","防晒"])
        df.at[index, "fit_skin"] = random.choice(["混合性","油性","干性","混合性偏干","中性","混合性偏油"])
        
        if row["type"] == 5:
            if "checkResult" in result and "description" in result["checkResult"]:
                df.at[index, "result_report"] = result.get("checkResult", {}).get("description", "")
            elif "antiaging" in result and "describe" in result["antiaging"]:
                df.at[index, "result_report"] = result.get("antiaging", {}).get("describe", "")
            else:
                df.at[index, "result_report"] = ""
        elif row["type"] == 3:
            df.at[index, "result_report"] = result.get("antiaging", {}).get("describe", "")
        else:
            df.at[index, "result_report"] = ""       
            
    df.to_excel("护肤品评测-推荐商品\评测-推荐商品-数据2.xlsx", index=False)
    print("数据处理完成")

def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJNVmZTOHF3ZTVGU3hqZkVJTzBrZ01SNTJ1SUhTSmtOQzMvemRJdDV0QUgwNHVjenptZjc2anREcE5HWjY4K29uSjRuazNXV3ZUVnphcmRFMGRjZzVqK09KNllvK3doZ1AvUlduaWFncTVtaENaR0pXMGN0MW12SnRQbnlRRGtYcyJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ1Mzc3NjEwNDY3LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIiLCJqdGkiOiIyY2U0N2E0Ny1hNzczLTQyYjUtOTY4ZC0xMzhkNzhiZDkzYmUiLCJleHAiOjE3NDU0NjQwMTAsImlhdCI6MTc0NTM3NzYxMCwic3ViIjoiUGVyaXBoZXJhbHMiLCJpc3MiOiJkZWNpc2lvblJiYWMifQ.Uzlo30UgG9iZ7Wx1sqEhAXsbRWELFXpq6cfpLhh9C5DOB8tfAN9ytpLszBgkiTgVFMGejQ5a3X58-2zGZeDdbQ"
    plugin_id = "ZJ940592f74c5f"
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("护肤品评测-推荐商品\评测-推荐商品-数据2.xlsx")
    for index, row in df[:15].iterrows():

        input_param = {
            "category": row["category"],
            "fit_skin": row["fit_skin"],
            "bagelToken":bagelToken,
            "history":[],
            "user_report":row["result_report"],
        }
        result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)
        print(result)
        result = json.loads(result)
        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        df.at[index, "API耗时"] = time_cost
        df.at[index, "tokens"] = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"护肤品评测-推荐商品\评测-推荐商品-数据2_{timestamp}.xlsx", index=False)
    print(f"结果已保存到护肤品评测-推荐商品\评测-推荐商品-数据2_{timestamp}.xlsx")


if __name__ == "__main__":
    # data_process()
    main()


