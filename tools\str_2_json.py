import json

def to_json(obj):
    """
    将字符串转换为json对象
    """
    s = obj
    if isinstance(obj, str):
        # 尝试移除多余的文本
        try:
            start = s.find("{")
            end = s.rfind("}")
            if start != -1 and end != -1:
                s = s[start:end+1]  # 提取 JSON 部分
                s = s.replace('```json', '').replace('```', '').replace('\\n', '').replace('\\', '')
        except Exception as e:
            print(f"处理字符串时出错: {e}")
    try:
        s = json.loads(s)
        return s
    except json.JSONDecodeError as e:
        print(f"不是有效的 JSON 字符串: {e}")
    return obj

if __name__ == "__main__":
    obj = """{ "dishName": "意大利肉丸面", "dishType": "肉菜", "calories": "大约1250KJ", "ingredients": [ {"name": "意大利面", "amount": "200g"}, {"name": "牛肉丸", "amount": "300g"}, {"name": "番茄酱", "amount": "100g"}, {"name": "罗勒叶", "amount": "10g"}, {"name": "橄榄油", "amount": "20g"}, {"name": "调味料", "amount": "10g"} ], "nutrients": [ {"name": "蛋白质", "amount": "40g"}, {"name": "脂肪", "amount": "20g"}, {"name": "碳水化合物", "amount": "80g"}, {"name": "纤维", "amount": "5g"}, {"name": "维生素C", "amount": "10mg"}, {"name": "铁", "amount": "5mg"} ] } 说明： - 热量计算基于一般食物的平均热量，具体数值会根据食物的实际成分和烹饪方法有所不同。 - 营养成分含量根据提供的食材和含量进行估算，实际情况可能会有所偏差。 - 菜品类型根据含有肉类成分，分类为“肉菜”。"""
    a = to_json(obj)
    print(type(a))
    print(a)