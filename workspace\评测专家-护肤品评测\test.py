# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import numpy as np
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from tools.numpy_2_python import convert_numpy_types

def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJNVmZTOHF3ZTVGU3hqZkVJTzBrZ01SNTJ1SUhTSmtOQzMvemRJdDV0QUgwNHVjenptZjc2anREcE5HWjY4K29uSjRuazNXV3ZUVnphcmRFMGRjZzVqK09KNllvK3doZ1AvUlduaWFncTVtaEk0c3ppYURPTGthSS95K1hYYmZUeCJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ1OTc2NzUzMzg4LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMDBkODU1OTA4NzJlZDNiMWI1IiwianRpIjoiYWRmYzlmOTQtNDQwZS00NDQ3LWFhYmMtNWJkN2JjMTkzNDQ5IiwiZXhwIjoxNzQ2MDYzMTUzLCJpYXQiOjE3NDU5NzY3NTMsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.bWsaeOLzAc5A808Ebn_zVmvrvooX_dDjQw5LMmwV08dww5pESmfUhOYB3-VYyTI0hR7QZHKdiWHAaboUiPDfZg"
    plugin_id = "ZJ0afddb86b46c"
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\评测专家-护肤品评测\评测专家-数据.xlsx")
    for index, row in df.iterrows():
        product_id = row["productId"]
        print(f"product_id: {product_id}")
        input_param = {
            "productId": convert_numpy_types(product_id),
        }
        
        result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)

        result = json.loads(result)
        df.at[index, "产品描述"] = json.dumps(result.get("data", {}).get("outPut", {}).get("productInfo", ""),ensure_ascii=False)
        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        df.at[index, "字数"] = len(result.get("data", {}).get("outPut", {}).get("content", ""))
        df.at[index, "API耗时"] = time_cost
        df.at[index, "tokens"] = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\评测专家-护肤品评测\评测专家-数据_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\评测专家-护肤品评测\评测专家-数据_{timestamp}.xlsx")


if __name__ == "__main__":
    # data_process()
    main()


