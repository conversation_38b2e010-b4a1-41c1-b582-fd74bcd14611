 
def main(params):
    content = None
    productList = None
    data = None
    productDescribe = ""
    
    if 'data' in params and params['data'] :
      data = params['data'] ;
      if 'recommendations' in data and data['recommendations']:
        productList = data['recommendations']
        for product in productList:
          if 'product_name' in product and product['product_name']:
            describe = "产品名称：" + product['product_name'] + ";"
          if 'recommended_dosage' in product and product['recommended_dosage']:
            describe += "推荐剂量：" + product['recommended_dosage'] + ";"
          if 'product_effect' in product and product['product_effect']:
            describe += "产品效果：" + product['product_effect'] + ";"
          if 'precautions' in product and product['precautions']:
            describe += "注意事项：" + product['precautions'] + ";" 
          productDescribe += describe + "。"
          
    if productList and len(productList) > 0 :
      content = '搭配以下保健品使用，可以更好的解决您的需求和问题:'
    else :
      content = '没找到适合你的保健品'; 
      
    healthProductCard = {
      "content": content,
      "productList" : productList
    }
    output_object ={
      "content": content,
      "healthProductCard": healthProductCard,
      "productDescribe": productDescribe
    }
     
    return output_object