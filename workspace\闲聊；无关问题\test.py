# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from utils.apiLLM import ApiLLM
from utils.agent_tester import AgentTester
from tools.evaluation_tool import EvaluationTool, process_evaluation_excel

bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzUxODY4NjM4MTc1LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMjBjODNmNzYxMWFkOWUwMDNlIiwianRpIjoiMDgxNWZhZjgtNWQ0MC00MzFmLTk5ZDUtMTkyZjU2YzYzZjgyIiwiZXhwIjoxNzUxOTU1MDM4LCJpYXQiOjE3NTE4Njg2MzgsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.tysBub-a4R65Pgj5p90AlPVwrOJQ4Ljx2zBFmv0-mq3ijUry2hbrPOWpW1UABFJf-Qg-5OMXxdndeSsIeSK1TA"
def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVQXFsc3VWc01Ic0RJY3JjYjJPZUFyS09uYTFERTdtZ2hUOTJSblpCYmJ6VlE9PSJ9"
    # bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzUwMTQyMzM2OTk2LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMjBjODNmNzYxMWFkOWUwMDNlIiwianRpIjoiMzU3NmRiYjYtZDcxMi00OTExLTgwZDEtMTQ0NGY0YzA4MjY1IiwiZXhwIjoxNzUwMjI4NzM3LCJpYXQiOjE3NTAxNDIzMzcsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.Cmgu6nR9vaxD3_qbzKzPWvM5h6NFZ2VJiptoWALldeUQ9w9e2fpxCRxREpqOf3akIdUiFhIvqcOrJsqRtbxnUQ"
    plugin_id = "ZJb792ddde0c98" # 3.1：ZJb792ddde0c98 3：ZJ1973dc41a30b
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\闲聊；无关问题\无关问题v3.1数据.xlsx")
    
    # 初始化所需的列
    required_columns = ["回答", "字数", "API耗时", "tokens"]
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""

    for index, row in df.iterrows():
        try:
            print(f"处理第 {index} 行")
            
            # 处理history
            if pd.isna(row["history"]):
                history = []
            else:
                try:
                    # 首先尝试直接解析
                    history = json.loads(row["history"])
                except json.JSONDecodeError:
                    try:
                        # 如果直接解析失败，尝试清理和修复JSON字符串
                        history_str = row["history"]
                        # 确保字符串以 [ 开始，以 ] 结束
                        if not history_str.strip().startswith('['):
                            history_str = '[' + history_str
                        if not history_str.strip().endswith(']'):
                            history_str = history_str + ']'
                        # 修复可能的格式问题
                        history_str = history_str.replace('}, {', '},{')  # 移除多余的空格
                        history_str = history_str.replace('} ,{', '},{')  # 修复逗号前的空格
                        history_str = history_str.replace('}, {', '},{')  # 修复逗号后的空格
                        
                        history = json.loads(history_str)
                    except json.JSONDecodeError as e:
                        print(f"JSON解析错误: {str(e)}")
                        print(f"问题数据: {row['history']}")
                        history = []  # 如果所有尝试都失败，使用空数组
            
            input_param = {
                "chat": row["chat"],
                "history": history,
                "micro_app_id": 836,
                "bagelToken": bagelToken,
                "bussinessDomain": row["bussinessDomain"],
                "emotional_type": row["emotional_type"],
                "emotional_strength": row["emotional_strength"],
                "app_name": row["app_name"],
            }

            result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)
            
            # 添加错误处理
            if result is None:
                print(f"API返回空结果，跳过第 {index} 行")
                continue
                
            try:
                result_json = json.loads(result)
                if result_json.get("code") != 0:  # 假设0是成功状态码
                    print(f"API返回错误: {result_json.get('msg')}")
                    continue
                    
                df.at[index, "回答"] = result_json.get("data", {}).get("outPut", {}).get("content", "")
                df.at[index, "字数"] = len(result_json.get("data", {}).get("outPut", {}).get("content", ""))
                print(f"字数{index}：{len(result_json.get('data', {}).get('outPut', {}).get('content', ''))}")
                df.at[index, "API耗时"] = time_cost
                token = count_tokens(result_json.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)
                print(f"tokens数{index}：{token}")
                df.at[index, "tokens"] = token
            except Exception as e:
                print(f"处理API结果时发生错误: {str(e)}")
                continue

        except Exception as e:
            print(f"处理第 {index} 行时发生错误: {str(e)}")
            continue

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\闲聊；无关问题\无关问题v3.1数据_{timestamp}.xlsx", index=False)
    print(f"workspace\闲聊；无关问题\无关问题v3.1数据_{timestamp}.xlsx")

def test_agent():
    tokenizer = get_v3_tokenizer()
    is_test_env = True
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1279,
            app_key="53ae2751e36c458cbbadc30a7341bf87",
            app_secret="fabdd34adbf04d6e5516100bc63626bd",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )
    
    path = "workspace\闲聊；无关问题\无关问题v3.1数据.xlsx"
    path_name = os.path.splitext(path)[0]
    df = pd.read_excel(path)
    
    new_column_names = ["首token时间", "总时间", "流式输出", "字数", "tokens数"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():
        # 处理history
        if pd.isna(row["history"]):
            history = []
        else:
            try:
                # 首先尝试直接解析
                history = json.loads(row["history"])
            except json.JSONDecodeError:
                try:
                    # 如果直接解析失败，尝试清理和修复JSON字符串
                    history_str = row["history"]
                    # 确保字符串以 [ 开始，以 ] 结束
                    if not history_str.strip().startswith('['):
                        history_str = '[' + history_str
                    if not history_str.strip().endswith(']'):
                        history_str = history_str + ']'
                    # 修复可能的格式问题
                    history_str = history_str.replace('}, {', '},{')  # 移除多余的空格
                    history_str = history_str.replace('} ,{', '},{')  # 修复逗号前的空格
                    history_str = history_str.replace('}, {', '},{')  # 修复逗号后的空格
                    
                    history = json.loads(history_str)
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {str(e)}")
                    print(f"问题数据: {row['history']}")
                    history = []  # 如果所有尝试都失败，使用空数组
        user_param = {
                "chat": row["chat"],
                "history": history,
                "micro_app_id": 836,
                "bagelToken": bagelToken,
                "bussinessDomain": row["bussinessDomain"],
                "emotional_type": row["emotional_type"],
                "emotional_strength": row["emotional_strength"],
                "app_name": row["app_name"],
            }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "流式输出"] = result["content"]
        df.at[index, "字数"] = len(result["content"])
        token = count_tokens(result["content"], tokenizer=tokenizer)
        df.at[index, "tokens数"] = token
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_path = f"{path_name}_智能体结果{timestamp}.xlsx"
    df.to_excel(new_path, index=False)
    print(f"结果已保存到{new_path}")    

def evaluation():
    # 示例用法
    prompt_template_path = "prompt\无关问题-评估模板.md"  # 替换为实际的提示词模板路径
    model_name = "gpt4o"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    final_output_path = f"workspace/闲聊；无关问题/评估结果_{model_name}_{timestamp}.xlsx"
    evaluation_tool = EvaluationTool(prompt_template_path, final_output_path, model_name=model_name)
    
    # 读取输入数据
    df = pd.read_excel("workspace\闲聊；无关问题\无关问题v3.1数据_20250707_163404.xlsx")
    
    # 定义输入列映射
    input_columns = {
        "chat": "chat",
        "history": "history",
        "bussinessDomain": "bussinessDomain",
        "emotional_type": "emotional_type",
        "emotional_strength": "emotional_strength",
        "app_name": "app_name",
    }

    # 执行批量评估
    result_df = evaluation_tool.evaluate_batch(
        df,
        input_columns=input_columns,  # 输入列名映射
        result_column="回答",         # 结果文本所在的列名
        output_column="评估结果"      # 输出列名
    )
    
    # 保存最终结果
    print(f"评估完成，结果已保存到: {final_output_path}")
    process_evaluation_excel(final_output_path, f"{final_output_path}_处理结果.xlsx")

if __name__ == "__main__":
    # main()
    # test_agent()
    evaluation()





