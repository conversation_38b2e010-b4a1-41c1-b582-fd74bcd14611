# -*- coding: utf-8 -*-
import json
import time
import requests
from typing import List, Dict, Any, Optional
from tools.str_2_json import to_json

class AgentTester:
    def __init__(self, 
                 micro_app_id: int,
                 app_key: str,
                 app_secret: str,
                 is_test_env: bool = True):
        """
        初始化Agent测试器
        
        Args:
            micro_app_id: 微应用ID
            app_key: 应用密钥
            app_secret: 应用密钥
            is_test_env: 是否为测试环境，默认为True
        """
        self.micro_app_id = micro_app_id
        self.app_key = app_key
        self.app_secret = app_secret
        self.is_test_env = is_test_env
        
        # 设置环境相关的URL
        base_url = "https://itest.clife.net" if is_test_env else "https://cms.clife.cn"
        self.get_token_url = f"{base_url}/agentpaas/token?appKey={app_key}&appSecret={app_secret}"
        self.stream_tool_url = f"{base_url}/agentpaas/agent/exeToolStream"
        self.create_session_url = f"{base_url}/agentpaas/agent/exeStreamSession"
        
        self.headers = {
            "Content-Type": "application/json",
        }
    
    def _get_token(self) -> Optional[str]:
        """获取访问令牌"""
        response = requests.get(self.get_token_url, headers=self.headers)
        if response.status_code == 200:
            result = response.json()
            return result['data']['token']
        return None
    
    def test_no_stream(self, 
                   content: str = "调用智能体",
                   user_param: Dict[str, Any] = {},
                   output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        执行非流式测试
        """
        # 获取token
        token = self._get_token()
        if not token:
            return {"error": "Failed to get token"}
            
        self.headers.update({"token": token})
        
        # 准备请求数据
        stream_session_data = {
            "userId": 0,
            "content": content,
            "microAppId": self.micro_app_id,
            "userParam": user_param
        }
        
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        response = requests.post(
            self.stream_tool_url,
            data=json.dumps(stream_session_data),
            headers=self.headers,
            stream=True,
            verify=False
        )
        if response.status_code == 200:
            result = {
                "request_time": time.time() - start_time,
                "content": response.text,
                "first_token_time": None,
                "total_time": None
            }
        result["total_time"] = time.time() - start_time
        return result
    def test_stream(self, 
                   content: str = "调用智能体",
                   user_param: Dict[str, Any] = {},
                   output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        执行流式测试
        
        Args:
            content: 测试内容
            user_param: 智能体入参
            output_file: 输出文件路径，如果为None则不输出到文件
            
        Returns:
            包含测试结果的字典
        """
        # 获取token
        token = self._get_token()
        if not token:
            return {"error": "Failed to get token"}
            
        self.headers.update({"token": token})
        
        # 准备请求数据
        stream_session_data = {
            "userId": 0,
            "content": content,
            "microAppId": self.micro_app_id,
            "userParam": user_param
        }
        
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        response = requests.post(
            self.stream_tool_url,
            data=json.dumps(stream_session_data),
            headers=self.headers,
            stream=True,
            verify=False
        )
        
        result = {
            "request_time": time.time() - start_time,
            "status_code": response.status_code,
            "content": "",
            "first_token_time": None,
            "total_time": None
        }
        
        if response.status_code == 200:
            first_token_received = False
            first_token_time = None
            buffer = b''
            temp = b''

            # 处理流式响应
            for chunk in response.iter_content(chunk_size=1024):
                # print(f"chunk: {chunk}")
                if not first_token_received and chunk:
                    temp = chunk
                    try:
                        text = temp.decode('utf-8')
                        # 处理可能有多行数据的情况
                        lines = text.strip().split('\n')
                        for line in lines:
                            if line.startswith('data:'):
                                try:
                                    # 去掉"data:"前缀
                                    json_str = line.replace('data:', '').strip()
                                    json_obj = json.loads(json_str)
                                    content = json_obj.get('content', '')
                                    if content:
                                        first_token_time = time.time()
                                        first_token_received = True
                                        result["first_token_time"] = first_token_time - start_time
                                        break
                                except json.JSONDecodeError:
                                    # 如果JSON解析失败，可能是不完整的数据
                                    pass
                    except UnicodeDecodeError:
                        pass                    
                
                if chunk:
                    buffer += chunk
                    try:
                        text = buffer.decode('utf-8')
                        # 处理可能有多行数据的情况
                        lines = text.strip().split('\n')
                        for line in lines:
                            if line.startswith('data:'):
                                try:
                                    # 去掉"data:"前缀
                                    json_str = line.replace('data:', '').strip()
                                    json_obj = json.loads(json_str)
                                    content = json_obj.get('content', '')
                                    if content:
                                        print(f"收到内容: {content}")
                                        result["content"] += content
                                except json.JSONDecodeError:
                                    # 如果JSON解析失败，可能是不完整的数据
                                    pass
                        buffer = b''
                    except UnicodeDecodeError:
                        pass
            
            if buffer:
                try:
                    print("--------------------------------")
                    print(buffer.decode('utf-8', errors='replace'), end='')
                    result["content"] += buffer.decode('utf-8', errors='replace')
                except:
                    pass
            
            result["total_time"] = time.time() - start_time
            print(f"首token时间: {result['first_token_time']}秒")
            print(f"总时间: {result['total_time']}秒")
            print(f"api结果: {result['content']}")
        return result
    
    def batch_test(self,
                  contents: List[str],
                  user_params: List[Dict[str, Any]],
                  output_file: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        批量执行测试
        
        Args:
            contents: 测试内容列表
            user_params: 用户参数列表
            output_file: 输出文件路径
            
        Returns:
            测试结果列表
        """
        results = []
        for content, user_param in zip(contents, user_params):
            result = self.test_stream(content, user_param, output_file)
            results.append(result)
            time.sleep(2)  # 添加延迟避免请求过于频繁
        return results
    
if __name__ == "__main__":
    # 测试环境配置
    test_config = {
        "micro_app_id": 1158,
        "app_key": "b8b3c3cc752649fcb2cf6d934cc88762",
        "app_secret": "6b9f6abe28ecb385704a360cb4eb9ce6"
    }
    
    # 创建测试器实例
    tester = AgentTester(**test_config)
    
    # 测试内容
    user_contents = [
        "这里面要刻一个好红色的眼睛就控制的很好的，没让他出去。这个口吃鸡眼晴爸，我已经画好了哦，你可以给爸爸讲一下，都放什么吗？太 阳照被照死了个影子个太阳，我刚拿着棒棒卡在嗯有小草鹿，嗯，这也是一个棒棒糖，这换了两个棒棒糖，刚掉到了地上啊。哇饿超人，土豆 超人是吗？嗯，你画的很有又发了，我又发了一个，我画了两个好看哦",
        "破害他哦哦。 让我开一个大香蕉。 哎呀，哈哈。 哈哈哈哈哈，你看看我刚才踩踩。 踩毁了你的向日葵，你看。 哎，干什么呀？他干嘛啊能干什么呀？打爸爸干嘛？对，你不歪了，我觉。 太阳吧，我又没破坏你的太阳花，不是我破坏的呀，宝贝。 姐姐告诉我，那你猜是谁破坏的呀？你出去刚才爸爸出去的时候，我都听到了这个玩意的笑声。 我也提高了，那怎么办呢？现在太阳花被破坏了。 嗯，我不想抽筋了，我已经很累了，完了，爸爸秀了。对，这竟然完了，对呀。 老弟睡觉。 你是不是在发现你的不满吗？嗯？",
        "这是我画的一幅画，这里面有一朵花。花有五棵花瓣叫五颜六色的花红色的花瓣，我要用来当雨伞。蓝色的花瓣，我准备把它撒在天上。粉色的花瓣是妈妈最喜欢的，我要送给她。黑色的花瓣，我不喜欢这个颜色，不要它。"
    ]
    
    # 用户参数
    user_params = [
        {
            "user_id": 0,
            "user_name": "张文林",
            "user_age": "3岁7个月",
            "user_gender": "男",
            "evaluation_dimension": dimension,
            "user_content": content
        }
        for content in user_contents
        for dimension in [1, 2, 3, 4, 5, 6, 7, 8]
    ]
    
    # 执行批量测试
    results = tester.batch_test(
        contents=["测试推荐"] * len(user_params),
        user_params=user_params,
        output_file="output.txt"
    )
    
    # 打印结果统计
    print(f"总测试数量: {len(results)}")
    print(f"成功数量: {sum(1 for r in results if r['status_code'] == 200)}")
    print(f"平均响应时间: {sum(r['total_time'] for r in results if r['total_time']) / len(results):.2f}秒")
