# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
# from tools.count_tokens import count_tokens, get_v3_tokenizer
from utils.apiLLM import v3_llm
from sklearn.metrics import classification_report, precision_recall_fscore_support, accuracy_score

def llm_check_coreference_resolution(original_query, resolved_query, history, standard_answer):
    """使用大模型判断指代消解是否成功"""
    try:
        # 初始化大模型客户端
        llm = v3_llm()

        # 构建提示词
        prompt = f"""
        请根据历史聊天记录，并参考标准答案，判断指代消解后的问题和原始问题是否表达了相同的意思，其中指代消解后的问题是原始问题的指代消解版本。
                    
        历史聊天记录：{history}
        原始问题：{original_query}
        指代消解后的问题：{resolved_query}
        标准答案：{standard_answer}

        请只回答"是"或"否"。如果两个问题表达了相同的意思，回答"是"；如果意思有显著差异，回答"否"。
        注意：判断时要考虑聊天记录中的上下文，确保指代消解后的句子能够正确还原原始问题中的指代词。
        """

        # 调用大模型
        messages = [{"role": "user", "content": prompt}]
        response = llm(messages=messages, stream=False)
        
        # 解析结果
        result = response.choices[0].message.content.strip().lower()
        print(f"解析结果: {result}")
        return 1 if "是" in result else 0

    except Exception as e:
        print(f"判断指代消解时出错: {str(e)}")
        return 0

def hfp_test(input_param, plugin_id, agent_sdk_token, test_mode=True):
    try:
        client = PluginTester(agent_sdk_token=agent_sdk_token, test_mode=test_mode)

        result, time_cost = client.execute_plugin(
            plugin_id=plugin_id,
            input_param=input_param
        )

        return result, time_cost
    except Exception as e:
        print(f"API请求出错: {str(e)}")
        return None, None

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVQXFsc3VWc01Ic0RJY3JjYjJPZUFyS09uYTFERTdtZ2hUOTJSblpCYmJ6VlE9PSJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ4MzM3NDIzNjY4LCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIxMDBkODU1OTA4NzJlZDNiMWI1IiwianRpIjoiODUyNDY2MzktNjM4OC00OGYzLWE5ZTMtNWExNTNiNDUzNTFmIiwiZXhwIjoxNzQ4NDIzODIzLCJpYXQiOjE3NDgzMzc0MjMsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.fckfObYPlaSnl4DuPUv9eBSDT-9-xN1tAx9LF8uGEgNXrvDx8uMG_2TE5ZJggChSC9Ja3H1Q59V2u3JhgEFibg"
    plugin_id = "ZJfe14729d13ec"

    df = pd.read_excel("workspace\指代消解+情绪识别\指代消解情绪识别-数据集2.xlsx")
    for index, row in df.iterrows():
        try:
            query = row["用户问题"]
            history = row["历史记录"]
            
            input_param = {
                "query": query,
                "history": history,
            }
            
            result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token, test_mode=True)
            
            if result is None:
                df.at[index, "改写后query"] = "API请求失败"
                df.at[index, "情绪类型"] = "API请求失败"
                df.at[index, "情绪强度"] = "API请求失败"
                df.at[index, "API耗时"] = "API请求失败"
                df.at[index, "消解成功"] = 0
                continue

            result = json.loads(result)
            resolved_query = result.get("data", {}).get("outPut", {}).get("query", "")
            df.at[index, "改写后query"] = resolved_query
            df.at[index, "情绪类型"] = result.get("data", {}).get("outPut", {}).get("emotional_type", "")
            df.at[index, "情绪强度"] = result.get("data", {}).get("outPut", {}).get("emotional_intensity", "")
            df.at[index, "API耗时"] = time_cost
            
            # 判断指代消解是否成功
            if resolved_query and resolved_query != "API请求失败":
                success = llm_check_coreference_resolution(query, resolved_query, history, row["改写后的用户问题"])
                df.at[index, "消解成功"] = success
                if df.at[index, "情绪类型"] == row["情绪识别"]:
                    df.at[index, "情绪识别正确"] = 1
                else:
                    df.at[index, "情绪识别正确"] = 0
            else:
                df.at[index, "消解成功"] = 0

            
        except Exception as e:
            print(f"处理第{index}行数据时出错: {str(e)}")
            df.at[index, "改写后query"] = f"处理出错: {str(e)}"
            df.at[index, "情绪类型"] = "处理出错"
            df.at[index, "情绪强度"] = "处理出错"
            df.at[index, "API耗时"] = "处理出错"
            df.at[index, "消解成功"] = 0
            continue

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    success_score(df)
    df.to_excel(f"workspace\指代消解+情绪识别\指代消解情绪识别-数据集2_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\指代消解+情绪识别\指代消解情绪识别-数据集2_{timestamp}.xlsx")

def main2():
    __path__ = "workspace\指代消解+情绪识别\指代消解情绪识别-数据集_20250603_095351.xlsx"
    df = pd.read_excel(__path__)
    for index, row in df.iterrows():
        original_query = row["用户问题"]
        resolved_query = row["改写后query"]
        history = row["历史记录"]
        success = llm_check_coreference_resolution(original_query, resolved_query, history)
        df.at[index, "消解成功"] = success
    df.to_excel(__path__, index=False)
    print(f"结果已保存到{__path__}")

def success_score(df, excel_path=None):
    # 只保留有标注和预测的行
    valid = df[~df["情绪识别"].isnull() & ~df["情绪类型"].isnull()]
    y_true = valid["情绪识别"].astype(str)
    y_pred = valid["情绪类型"].astype(str)

    # 计算各类指标
    report = classification_report(y_true, y_pred, digits=4)
    print("情绪识别分类报告：")
    print(report)

    # 增加混淆矩阵输出
    from sklearn.metrics import confusion_matrix
    import numpy as np
    labels = sorted(list(set(y_true) | set(y_pred)))
    cm = confusion_matrix(y_true, y_pred, labels=labels)
    print("混淆矩阵：")
    print("标签顺序：", labels)
    print(np.array(cm))
    cm_df = pd.DataFrame(cm, index=labels, columns=labels)

    # 计算accuracy
    accuracy = accuracy_score(y_true, y_pred)

    # macro平均
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro'
    )
    print(f"Macro平均准确率: {precision:.4f}")
    print(f"Macro平均召回率: {recall:.4f}")
    print(f"Macro平均F1分数: {f1:.4f}")

    # weighted平均
    precision_w, recall_w, f1_w, _ = precision_recall_fscore_support(
        y_true, y_pred, average='weighted'
    )
    print(f"加权平均准确率: {precision_w:.4f}")
    print(f"加权平均召回率: {recall_w:.4f}")
    print(f"加权平均F1分数: {f1_w:.4f}")

    metrics_df = pd.DataFrame({
        "指标": ["Macro Precision", "Macro Recall", "Macro F1", "Weighted Precision", "Weighted Recall", "Weighted F1", "Accuracy"],
        "值": [precision, recall, f1, precision_w, recall_w, f1_w, accuracy]
    })

    if excel_path is not None:
        with pd.ExcelWriter(excel_path) as writer:
            cm_df.to_excel(writer, sheet_name="混淆矩阵")
            metrics_df.to_excel(writer, sheet_name="指标")
        print(f"混淆矩阵和指标已保存到 {excel_path}")


    return precision, recall, f1, precision_w, recall_w, f1_w
            
if __name__ == "__main__":
    main()
    # main2()

    # excel_path = "workspace\指代消解+情绪识别\指代消解情绪识别-数据集2_20250703_180728_指标.xlsx"
    # df = pd.read_excel("workspace\指代消解+情绪识别\指代消解情绪识别-数据集2_20250703_180728.xlsx")
    # success_score(df, excel_path=excel_path)

