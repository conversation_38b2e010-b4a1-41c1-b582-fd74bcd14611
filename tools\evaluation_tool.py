import pandas as pd
import json
import os
import re
import sys
import datetime
import requests
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
sys.path.append(parent_dir)
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from utils.apiLLM import ApiLLM, qwen_llm, gpt4o_llm
from tools.count_tokens import count_tokens, get_v3_tokenizer


class EvaluationTool:
    def __init__(self, prompt_template_path, output_excel_path=None, model_name='qwen'):
        """
        初始化评估工具
        
        Args:
            prompt_template_path (str): 提示词模板文件路径
            output_excel_path (str, optional): 输出Excel文件路径，如果为None则自动生成
        """
        self.prompt_template_path = prompt_template_path
        self.output_excel_path = output_excel_path or f'评估结果_{datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}.xlsx'
        self.tokenizer = get_v3_tokenizer()
        if model_name == 'qwen':
            self.llm = qwen_llm()
        elif model_name == 'gpt4o':
            self.llm = gpt4o_llm()
        
        # 加载提示词模板
        if os.path.exists(prompt_template_path):
            with open(prompt_template_path, 'r', encoding='utf-8') as f:
                self.prompt_template = f.read()
        else:
            raise FileNotFoundError(f"提示词模板文件 '{prompt_template_path}' 未找到")

    def _extract_json_from_markdown(self, md_string):
        """从markdown文本中提取JSON内容"""
        if not md_string:
            return None
        match = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", md_string, re.DOTALL)
        if match:
            return match.group(1).strip()
        try:
            json.loads(md_string.strip())
            return md_string.strip()
        except json.JSONDecodeError:
            pass
        return None

    def _call_llm_api(self, prompt_text, max_retries=3, backoff_factor=0.5):
        """调用大模型API"""
        print(f"--- 调用大模型API ---")
        
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=backoff_factor,
            status_forcelist=[500, 502, 503, 504],
        )
        
        try:
            # 构建消息格式
            messages = [{"role": "user", "content": prompt_text}]
            response = self.llm(messages=messages, stream=False)
            
            if hasattr(response, 'choices') and len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                print("API返回结果格式异常")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"API调用失败: {str(e)}")
            if "NameResolutionError" in str(e):
                print("DNS解析失败，请检查网络连接和服务器地址是否正确")
            elif "ConnectionError" in str(e):
                print("连接错误，请检查网络连接是否正常")
            elif "Timeout" in str(e):
                print("请求超时，服务器响应时间过长")
            else:
                print(f"未知错误: {type(e).__name__}")
            return f"API调用失败: {str(e)}"
        except Exception as e:
            print(f"发生未知错误: {str(e)}")
            return f"处理失败: {str(e)}"

    def _process_history(self, history):
        """处理历史记录数据"""
        if pd.isna(history):
            return []
        try:
            if isinstance(history, str):
                return json.loads(history)
            return history
        except json.JSONDecodeError:
            try:
                history_str = str(history)
                if not history_str.strip().startswith('['):
                    history_str = '[' + history_str
                if not history_str.strip().endswith(']'):
                    history_str = history_str + ']'
                history_str = history_str.replace('}, {', '},{')
                history_str = history_str.replace('} ,{', '},{')
                return json.loads(history_str)
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {str(e)}")
                print(f"问题数据: {history}")
                return []

    def _format_input_params(self, input_data):
        """
        将输入参数字典格式化为多行字符串，便于prompt中替换{input_params}
        """
        lines = []
        for k, v in input_data.items():
            if isinstance(v, (dict, list)):
                v_str = json.dumps(v, ensure_ascii=False)
            else:
                v_str = str(v)
            lines.append(f"{k}：{v_str}")
        return "\n".join(lines)

    def evaluate(self, input_data, result_text=None):
        """
        评估输入数据和结果
        
        Args:
            input_data (dict): 输入参数字典
            result_text (str, optional): 需要评估的结果文本
        
        Returns:
            dict: 评估结果
        """
        # 处理提示词模板
        filled_prompt = self.prompt_template
        # 新增 input_params 替换
        if "{input_params}" in filled_prompt:
            input_params_str = self._format_input_params(input_data)
            filled_prompt = filled_prompt.replace("{input_params}", input_params_str)
        # 如果提供了结果文本，添加到提示词中
        if result_text:
            filled_prompt = filled_prompt.replace("{result}", str(result_text))

        # 调用API获取评估结果
        evaluation_result = self._call_llm_api(filled_prompt)
        
        # 提取JSON结果
        json_result = self._extract_json_from_markdown(evaluation_result)
        if json_result:
            try:
                json_r = json.loads(json_result)
                print(f"JSON结果:{json_r}")
                return json_r
            except json.JSONDecodeError:
                print("JSON解析失败")
                return None
        return None

    def evaluate_batch(self, df, input_columns=None, result_column="回答", output_column="评估结果"):
        """
        批量评估DataFrame中的数据
        
        Args:
            df (pd.DataFrame): 输入数据DataFrame
            input_columns (dict): 输入列名映射，格式为 {param_name: column_name}
            result_column (str): 结果文本所在的列名
            output_column (str): 输出列名
        
        Returns:
            pd.DataFrame: 包含评估结果的DataFrame
        """
        if output_column not in df.columns:
            df[output_column] = pd.NA

        # 如果没有提供input_columns，使用默认映射
        if input_columns is None:
            input_columns = {
                "userInfo": "userInfo",
                "userReport": "userReport",
                "history": "history",
                "query": "query",
                "expire": "expire"
            }

        for index, row in df.iterrows():
            print(f"\n--- 正在处理行 {index + 1} / {len(df)} ---")
            
            try:
                # 构建输入参数字典
                input_data = {}
                for param_name, column_name in input_columns.items():
                    if column_name in row:
                        value = row[column_name]
                        if pd.isna(value):
                            value = None
                        input_data[param_name] = value
                
                # 获取结果文本
                result_text = None
                if result_column in row:
                    result_text = row[result_column]
                    if pd.isna(result_text):
                        result_text = None
                
                # 执行评估
                evaluation_result = self.evaluate(input_data, result_text)
                
                # 保存结果
                if evaluation_result:
                    df.at[index, output_column] = json.dumps(evaluation_result, ensure_ascii=False)
                
                # 保存中间结果
                try:
                    df.to_excel(self.output_excel_path, index=False)
                    print(f"行 {index + 1}: 结果已更新并保存到 '{self.output_excel_path}'")
                except Exception as e:
                    print(f"保存Excel文件时出错: {str(e)}")
                    
            except Exception as e:
                print(f"处理行 {index + 1} 时发生错误: {str(e)}")
                continue

        return df

def extract_json_from_str(text):
    """从字符串中提取JSON部分"""
    try:
        # 查找第一个 { 和最后一个 } 之间的内容
        pattern = r'\{[\s\S]*\}'
        match = re.search(pattern, text)
        if match:
            return match.group(0)
        return None
    except Exception as e:
        print(f"提取JSON时出错: {str(e)}")
        return None

def extract_evaluation_data(json_str):
    """从JSON字符串中提取评估数据"""
    try:
        # 首先提取JSON部分
        json_content = extract_json_from_str(json_str)
        if not json_content:
            print("未找到有效的JSON内容")
            return {}
            
        data = json.loads(json_content)
        result = {}
        
        # 遍历所有维度
        for dimension, dim_info in data['evaluation_dimensions'].items():
            # 遍历子维度
            for sub_dim, sub_info in dim_info['sub_dimensions'].items():
                # 使用子维度名称作为键
                sub_dim_name = sub_info['name']
                # 存储分数
                result[sub_dim_name] = sub_info['score']
                # 存储反馈
                result[f'反馈（{sub_dim_name}）'] = sub_info['feedback']
        
        return result
    except Exception as e:
        print(f"解析JSON时出错: {str(e)}")
        return {}

def process_evaluation_excel(input_file, output_file):
    """处理Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file)
        
        # 确保存在"大模型评估结果"列
        if '评估结果' not in df.columns:
            print("Excel文件中没有找到'评估结果'列")
            return
        
        # 创建新的DataFrame来存储结果
        all_results = []
        
        # 处理每一行
        for index, row in df.iterrows():
            json_str = row['评估结果']
            if pd.isna(json_str):
                continue
                
            # 提取数据
            result = extract_evaluation_data(json_str)
            if result:
                all_results.append(result)
        
        # 创建结果DataFrame
        result_df = pd.DataFrame(all_results)
        
        # 保存到新的Excel文件
        result_df.to_excel(output_file, index=False)
        print(f"处理完成，结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理Excel文件时出错: {str(e)}")

def main():
    # 示例用法
    prompt_template_path = "prompt\报告分析-评估模板.md"  # 替换为实际的提示词模板路径
    model_name = "gpt4o"
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    final_output_path = f"workspace/通用-报告分析/评估结果_{model_name}_{timestamp}.xlsx"
    evaluation_tool = EvaluationTool(prompt_template_path, final_output_path, model_name=model_name)
    
    # 读取输入数据
    df = pd.read_excel("workspace\通用-报告分析\报告分析数据.xlsx_20250625_115129.xlsx")
    
    # 定义输入列映射
    input_columns = {
        "userInfo": "userInfo",
        "userReport": "userReport",
        "history": "history",
        "query": "query",
        "expire": "expire",
        "type":"type"
    }

    # 执行批量评估
    result_df = evaluation_tool.evaluate_batch(
        df,
        input_columns=input_columns,  # 输入列名映射
        result_column="回答",         # 结果文本所在的列名
        output_column="评估结果"      # 输出列名
    )
    
    # 保存最终结果
    print(f"评估完成，结果已保存到: {final_output_path}")
    process_evaluation_excel(final_output_path, f"{final_output_path}_处理结果.xlsx")

if __name__ == "__main__":
    main()
    # process_evaluation_excel("workspace\通用-报告分析\评估结果_gpt4o_20250625_162559.xlsx", f"workspace\通用-报告分析\评估结果_gpt4o_20250625_162559_处理结果.xlsx")