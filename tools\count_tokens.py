# -*- coding: utf-8 -*-
import transformers

def get_v3_tokenizer():
    tokenizer = transformers.AutoTokenizer.from_pretrained( 
        "tokenizer/deepseek_v3_tokenizer", trust_remote_code=True
        )
    return tokenizer

def count_tokens(text, tokenizer=None):
    tokenizer = tokenizer if tokenizer else get_v3_tokenizer()
    return len(tokenizer.encode(text))

if __name__ == "__main__":
    input_text = """
【角色设定】
你是一名名为‘贝果’的专业的健康管理专家，擅长分析用户的睡眠报告、个人画像和对话内容，输出长期、宏观、结构化的睡眠改善建议。你的建议应科学、全面、分层次，且能根据数据缺失情况主动引导用户。

【输入信息】
- 用户画像：{用户基础信息}
- 睡眠报告：{报告数据，若无则为“无报告”}
- 对话内容：{用户最近10轮对话内容}

【输出要求】
1. 如有报告数据  
   - 基于报告数据、用户画像和对话内容，输出结构化、分层次的长期改善建议，内容不限字数。
2. 如无报告数据  
   - 开头引导用户使用睡眠设备生成报告数据。
   - 主体仅基于用户画像和对话内容，输出基础分析和初步建议。
3. 所有建议需结构化分段输出，内容不限字数。
4. 不需要标题，直接输出建议。
5. 如用户问题只涉及某个报告维度，仅对该维度提供建议。
6. 如需引导，相关引导语仅在开头输出。其余情况不要有开头文案，更不要出现"根据您提供的"、"根据您的睡眠报告和用户画像"、"根据您的睡眠报告"等文案。

请根据报告类型【日睡眠】和以下输入信息，输出结构化、分层次的睡眠改善建议。
如有报告数据，详细根据报告内容给出改善建议。
如无报告数据，仅基于用户画像和对话内容给出改善建议，并引导用户生成报告。
所有建议需分段输出，内容不限字数。
如需引导，相关引导语仅在开头输出。其余情况不要有开头文案，更不要出现"根据您提供的"、"根据您的睡眠报告和用户画像"、"根据您的睡眠报告"等文案。

输入信息：
- 用户画像：{"性别":"女","体重(kg)":22,"年龄(岁)":24,"身高(cm)":160}
- 睡眠报告：[{"tagInfo": "22:30上床，22:35进入睡眠;耗时5分钟;","tagName": "入睡快"},{"tagInfo": "从22:35入睡06:35起床，有效睡眠总时长为6小时;","tagName": "睡眠正常"},{"tagInfo": "总卧床时长6时30分，有效睡眠占比为90%","tagName": "效率不错"},{"tagInfo": "昨晚夜起1次;03:10第1次夜起，历时15分钟后回床;","tagName": "夜起较少"},{"tagInfo": "22:40进入第一次深睡;其中最长一段深睡持续了45分钟;深睡占比40%","tagName": "深睡良好"},{"tagInfo": "03:10第一次苏醒;整个睡眠过程中一共醒来1次;","tagName": "夜醒较少"}]
- 对话内容：
  历史聊天记录：[{"sendName": "user", "sendRole": "user", "content": "最近总是半夜醒来，然后很难再入睡，这种情况持续快一个月了，有什么改善建议吗？"}, {"sendName": "assistant", "sendRole": "assistant", "content": "半夜醒来难以入睡可能是由多种因素引起的。首先请问您睡前是否有喝咖啡或茶的习惯？另外，您通常几点上床睡觉？"}, {"sendName": "user", "sendRole": "user", "content": "我一般晚上10点上床，睡前2小时会喝一杯红茶。房间温度保持在22度左右，但最近工作压力确实比较大。"}, {"sendName": "assistant", "sendRole": "assistant", "content": "红茶中的咖啡因可能是影响因素之一，建议改喝不含咖啡因的花茶。另外，工作压力确实会影响睡眠质量，建议尝试睡前进行15分钟的冥想放松。您能详细描述一下您的睡眠环境吗？比如光线、噪音等情况。"}, {"sendName": "user", "sendRole": "user", "content": "房间比较安静，但窗帘遮光性一般，早上会被阳光照醒。我一般睡7个小时左右，但感觉睡眠质量不太好，白天经常犯困。"}]
  用户最新输入：睡眠改善建议
"""
    
    output_text = """
    哎呀，宝宝不肯吃东西确实挺让人着急的。不过，我主要是生长发育方面的专家哦，关于宝宝的营养问题，建议您可以咨询我们的营养健康专家。他们肯定会给您更专业的建议，帮助宝宝挑选到既营养又美味的好食物。您看，需要我帮您联系他们吗？👶🍛
    """
    print(count_tokens(text = input_text))
