import json
text = """[ { "sendName": "user", "sendRole": "user", "content": "今天的天气真好，带孩子出去玩很开心呢！" },{ "sendName": "自由对话", "sendRole": "自由对话", "content": "哇，听你说今天的天气这么好，带孩子出去玩真是太棒了！🌞🌳 能感受到你和孩子的快乐，真是让人心情也跟着明媚起来呢！你们都玩了些什么有趣的活动呀？有没有什么特别的发现或者好玩的小故事可以分享给我呢？😊 顺便提一下，如果你在带孩子玩耍的过程中，有任何关于孩子生长发育方面的小疑问，比如如何通过户外活动促进孩子的身心发展，或者有哪些适合孩子成长的小游戏，随时都可以问我哦！我会很乐意为你提供一些建议和帮助的。" },{ "sendName": "user", "sendRole": "user", "content": "今天的天气真好，带孩子出去玩很开心呢！" },{ "sendName": "自由对话", "sendRole": "自由对话","content": "哇，看来你今天的心情和天气一样美好呢！🌤️ 带孩子出去玩，不仅能享受美好的亲子时光，还能让孩子在大自然中尽情探索，真是两全其美啊！😄 你们今天有没有尝试什么新的游戏或者活动呢？孩子一定玩得很开心吧！ 说到户外活动，其实它对孩子的生长发育有着很多积极的影响哦。比如，晒晒太阳可以帮助孩子补充维生素D，促进钙的吸收，对骨骼发育特别有好处。还有，户外活动能增强孩子的体质，提高免疫力，对身心发展都大有裨益。 如果你对如何在日常生活中更好地促进孩子的生长发育有什么疑问，或者想了解一些适合孩子成长的小游戏和活动，随时欢迎来问我哦！我会根据孩子的年龄和兴趣，给你提供一些实用的建议。🌱 对了，如果你对其他健康领域比如营养、皮肤、睡眠、运动等方面也有兴趣，我们这里还有其他专业的智能体可以为你提供帮助哦！比如营养健康专家、睡眠专家等等。总之，有什么问题尽管提，我们一起为孩子的健康成长保驾护航！"}]"""
history = json.loads(text)
print(history)
