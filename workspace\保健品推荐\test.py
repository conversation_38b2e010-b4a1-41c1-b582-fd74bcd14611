# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from utils.apiLLM import ApiLLM

def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVQWNCT2tFWXNJS2dzc3FQUlVpeDVCM0JZR01PTlZMZDZYTTN3VUZLMkVhUVE9PSJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ5NjA0NDkzMTU0LCJ1c2VyTmFtZSI6IjE5NTI5OTc3NzkxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6Nzc4LCJkZXZpY2UiOiIiLCJqdGkiOiJjMGViNjU0Zi1mODc5LTRjYjctOWQzZi0zYzQxMWI3ZDgwODMiLCJleHAiOjE3NDk2OTA4OTMsImlhdCI6MTc0OTYwNDQ5Mywic3ViIjoiUGVyaXBoZXJhbHMiLCJpc3MiOiJkZWNpc2lvblJiYWMifQ.oa26cdCp2JrOytoCWrb6dZduyJIcCuB4HEw9yjMUCicjhH5Ha7VcY5oOglZhcGEfBszPinIYXkukGX0NYgVe5Q"
    plugin_id = "ZJ09783bee3145"
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\护肤品推荐官-推荐商品\推荐商品-数据3.0.xlsx")
    for index, row in df.iterrows():
        benefit = json.loads(row["benefit"]) if not pd.isna(row["benefit"]) else []
        skin_problem = json.loads(row["skin_problem"]) if not pd.isna(row["skin_problem"]) else []
        
        print(f"处理第 {index} 行")
        print(f"benefit: {benefit}")
        print(f"skin_problem: {skin_problem}")
        
        input_param = {
            "bagelToken": bagelToken,
            "benefit": benefit,
            "skin_problem": skin_problem,
        }
        
        result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)

        result = json.loads(result)
        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        # print(f"回答{index}：{result.get('data', {}).get('outPut', {}).get('content', '')}")
        
        describe_value = result.get("data", {}).get("outPut", {}).get("productDescribe", "")
        print(f"Describe值: {describe_value}")  # 添加调试输出
        df.at[index, "productDescribe"] = describe_value
        
        df.at[index, "字数"] = len(result.get("data", {}).get("outPut", {}).get("content", ""))
        print(f"字数{index}：{len(result.get('data', {}).get('outPut', {}).get('content', ''))}")
        
        df.at[index, "API耗时"] = time_cost
        
        token = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)
        print(f"tokens数{index}：{token}")
        df.at[index, "tokens"] = token

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\保健品推荐\保健品推荐-数据3.0_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\保健品推荐\保健品推荐-数据3.0_{timestamp}.xlsx")
    

if __name__ == "__main__":
    # data_process()
    main()


