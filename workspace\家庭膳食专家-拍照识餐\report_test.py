# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
import numpy as np
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime, time
from tools.count_tokens import count_tokens, get_v3_tokenizer
from tools.numpy_2_python import convert_numpy_types
from utils.agent_tester import AgentTester

def test_plugin(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main(path, image_type):
    # todo 需要修改
    
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJNVmZTOHF3ZTVGU3hqZkVJTzBrZ01SNTJ1SUhTSmtOQzMvemRJdDV0QUgwNHVjenptZjc2anREcE5HWjY4K29uSjRuazNXV3ZUVnphcmRFMGRjZzVqK09KNllvK3doZ1AvUlduaWFncTVtakRoZzZzTlpibWFvK05ERGdKY1V5VSJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ1NDczNzA1NzgyLCJ1c2VyTmFtZSI6IjE4OTI1MTE0MTcxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6NDk5LCJkZXZpY2UiOiIiLCJqdGkiOiJlNzA3YWViOS01OGIzLTQ1NWEtYjk3ZS0wMWRlNzNkNTQ1YTciLCJleHAiOjE3NDU1NjAxMDUsImlhdCI6MTc0NTQ3MzcwNSwic3ViIjoiUGVyaXBoZXJhbHMiLCJpc3MiOiJkZWNpc2lvblJiYWMifQ.MSMOVLgYJuGxQrK6LT-wLiDt0aLbl_nJAw-xsTx5pqSIiEibJlHYWaCCybhChhdayBmU-Att7b5tKz-gRvvptQ"
    plugin_id = "ZJ660faca0faaf"
    tokenizer = get_v3_tokenizer()

    # 读取JSON文件
    with open(path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 提取所有图片URL并转换为指定格式
    image_urls = [[{"url": item['url']}] for item in data[:30]]  # 食材38张 菜品100张
    print(f"成功读取 {len(image_urls)} 个图片URL")

    df = pd.DataFrame()
    # 处理每个图片
    for i, imagelist in enumerate(image_urls, 1):
        # 将'换成"
        temp_imagelist = imagelist
        imagelist = str(imagelist).replace("'", '"')
        print(f"\n处理第 {i}/{len(image_urls)} 个图片:{imagelist}")
        input_param = {
            "imageList": imagelist,
            "imageType": image_type
        }
        result, time_cost = test_plugin(input_param, plugin_id, agent_sdk_token)
        
        # 保存单个结果
        df.at[i, "image_index"] = i
        df.at[i, "image_url"] = temp_imagelist[0]['url']
        df.at[i, "API耗时"] = time_cost
        df.at[i, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        df.at[i, "字数"] = len(result.get("data", {}).get("outPut", {}).get("content", ""))
        df.at[i, "tokens"] = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)


if __name__ == "__main__":
    main()


