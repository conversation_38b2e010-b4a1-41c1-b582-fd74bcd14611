import requests
import os
import json
import re
from datetime import datetime

def upload_image(upload_url, file_path, headers):
    """
    上传照片到指定的URL。

    :param upload_url: 上传照片的接口URL
    :param file_path: 文件路径
    :param headers: 请求头
    :return: 响应对象
    """
    try:
        with open(file_path, 'rb') as f:
            # 构建multipart/form-data格式的文件数据，将'file'改为'avatar'
            files = {
                'avatar': (os.path.basename(file_path), f, 'image/jpeg')
            }
            
            # 发送POST请求
            with requests.Session() as session:
                response = session.post(upload_url, files=files, headers=headers)
                return response
    except Exception as e:
        print(f"上传文件时发生错误: {str(e)}")
        return None


def upload_images(images_dir):
    # 定义上传URL
    upload_url = 'https://itest.clife.net/decision/agent-microapp/file/upload'
    # 定义请求头
    headers = {
        'Authorization': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQyODcyNjQyMDE1LCJ1c2VyTmFtZSI6Iui0neaenOS4k-mhuei0puWPtyIsInVzZXJJZCI6MTA3NiwianRpIjoiYTNiMGE3ZjAtZTBhNi00ZjAxLTlkZTgtYWRlODk1YzMzNGVjIiwiZXhwIjoxNzQyOTU5MDQyLCJpYXQiOjE3NDI4NzI2NDIsInN1YiI6IlBlcmlwaGVyYWxzIiwiaXNzIjoiZGVjaXNpb25SYmFjIn0.gQKFn39MtWaF33G2kAHNZoeMfSPPywwvMuBBzG22c9rLYEfRlaR2bQ-w-bR-GU9VL7XPtJGurBJ809J4kMcFog',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7,en;q=0.6',
        'Origin': 'https://itest.clife.net',
        'Referer': 'https://itest.clife.net/web-agent-paas/',
        'Sec-Ch-Ua': '"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        'Sign': 'c9e477c99c6b7517ddccc53d0de42085e755b43bd60521856b90925be60520a7a8685f4a2d76b28f2d2eca3ef937333e6cc8f974ed2a106d0de1b72682fceac0'
    }
    
    uploaded_urls = []

    # 确保输出目录存在
    os.makedirs('upload_images2paas\upload_results', exist_ok=True)
    
    # 获取当前时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 创建日志文件
    log_file = f'upload_images2paas/upload_results/log/upload_log_{timestamp}.txt'
    
    try:
        # 遍历目录中的所有文件
        for file_name in os.listdir(images_dir):
            if file_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                file_path = os.path.join(images_dir, file_name)
                
                print(f"\n正在上传文件: {file_name}")
                
                # 上传文件
                response = upload_image(upload_url, file_path, headers)
                
                if response and response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"服务器返回数据: {data}")
                        
                        if isinstance(data, dict):
                            if data.get('success', False):
                                url = data.get('data', '')
                                if url:
                                    uploaded_urls.append({
                                        'file_name': file_name,
                                        'url': url,
                                        'status': 'success',
                                        'timestamp': datetime.now().isoformat()
                                    })
                                    print(f"✓ 上传成功: {file_name}")
                                    print(f"  URL: {url}")
                            else:
                                error_msg = data.get('errMessage', '未知错误')
                                print(f"✗ 上传失败: {error_msg}")
                        else:
                            print(f"✗ 服务器返回的数据格式不正确: {data}")
                            
                    except Exception as e:
                        print(f"✗ 处理响应数据时出错: {str(e)}")
                else:
                    print(f"✗ 上传失败: HTTP {response.status_code if response else 'No response'}")
                    if response:
                        print(f"错误响应: {response.text}")
                
                # 保存进度
                with open(f'upload_results/results_{timestamp}.json', 'w', encoding='utf-8') as f:
                    json.dump(uploaded_urls, f, ensure_ascii=False, indent=2)
                
                # 记录日志
                with open(log_file, 'a', encoding='utf-8') as f:
                    f.write(f"[{datetime.now().isoformat()}] {file_name}: {'成功' if response and response.status_code == 200 else '失败'}\n")
                
    except Exception as e:
        print(f"程序执行过程中发生错误: {str(e)}")
    finally:
        # 保存最终结果
        if uploaded_urls:
            with open(f'upload_results/final_cai_results_{timestamp}.json', 'w', encoding='utf-8') as f:
                json.dump(uploaded_urls, f, ensure_ascii=False, indent=2)
            print(f"\n上传完成！成功上传 {len(uploaded_urls)} 个文件")
            print(f"详细结果已保存到: upload_results/final_results_{timestamp}.json")
            print(f"上传日志已保存到: {log_file}")
        else:
            print("\n没有文件被成功上传")

if __name__ == '__main__':
    images_dir = 'C:/Users/<USER>/Desktop/测试菜谱图片'
    upload_images(images_dir)