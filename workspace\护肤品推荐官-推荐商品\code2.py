 
def main(params):
  
  likeProductList = None;   
  
  recommendProducList = None;  
   
  recommendProduCount = 0
  
  productDescribe = ''
  
  recommendCombList = []
  likeCombList = []
  
  unit = None
  if 'unit' in params and params['unit'] :
    unit = params['unit']
   
  if 'data' in params and params['data'] and 'product' in params['data'] and params['data']['product']:  
    product = params['data']['product']
     
    if unit and unit == '组合' :
      # 推荐商品组合例表
      if 'recommend_product' in product and product['recommend_product'] :
        recommendCombList = transCombList(product['recommend_product'])
        for item in recommendCombList :
          if 'product' in item and item['product'] :
            productList = item['product']
            recommendProduCount += len(productList)
           
        
      # 感兴趣的商组合品例表
      if 'interest_product' in product and product['interest_product'] :
        likeCombList = transCombList(product['interest_product'])
      
    else : 
      # 推荐商品例表
      if 'recommend_product' in product and product['recommend_product'] :
        recommendProducList = converProduct(product['recommend_product'])
        recommendProduCount = len(recommendProducList)
        
      # 感兴趣的商品例表
      if 'interest_product' in product and product['interest_product'] :
        likeProductList = converProduct(product['interest_product'])
        
  
    #生成推荐组合描述信息
    if recommendCombList : 
      for recommendComb in recommendCombList :
        if 'product' in recommendComb and recommendComb['product'] :
          ProductList = recommendComb['product']
          for product in ProductList :
            describe = "肤护品名称：" + product['product_name'] 
          
            if 'product_brand' in product and product['product_brand'] :
              describe += "; 品牌:" + product['product_brand']
            
            if 'product_subcategory' in product and product['product_subcategory'] :
              describe += "; 护肤品类别:" + product['product_subcategory']
            
            if 'benefits' in product and product['benefits'] :
              describe += "; 功效:" + "、".join(product['benefits'])
            
            if 'suitable_for_skin_type' in product and product['suitable_for_skin_type'] :
              describe += "; 适合肤质:" + product['suitable_for_skin_type']
              
            if 'key_ingredients' in product and product['key_ingredients'] :
              describe += "; 关键成分:" + product['key_ingredients']
            
            if 'safety_score' in product and product['safety_score'] :
              describe += "; 安全评分（满分5分）:" + str(product['safety_score'])
            
            # if 'ingredient_explanation' in product and product['ingredient_explanation'] :
            #   describe += "; 成分解释:" + product['ingredient_explanation'];
            
            # if 'place_of_origin' in product and product['place_of_origin'] :
            #   describe += "; 产地:" + product['place_of_origin'];  
            productDescribe += describe + "。"
    elif recommendProducList : 
      for product in recommendProducList :
        describe = "肤护品名称：" + product['product_name'] 
        if 'product_brand' in product and product['product_brand'] :
          describe += "; 品牌:" + product['product_brand']
        
        if 'product_subcategory' in product and product['product_subcategory'] :
          describe += "; 护肤品类别:" + product['product_subcategory']
            
        if 'benefits' in product and product['benefits'] :
          describe += "; 功效:" + "、".join(product['benefits'])
            
        if 'suitable_for_skin_type' in product and product['suitable_for_skin_type'] :
          describe += "; 适合肤质:" + product['suitable_for_skin_type']; 
        
        if 'key_ingredients' in product and product['key_ingredients'] :
          describe += "; 关键成分:" + product['key_ingredients']
        
        if 'safety_score' in product and product['safety_score'] :
          describe += "; 安全评分（满分5分）:" + str(product['safety_score'])
        
        if 'ingredient_explanation' in product and product['ingredient_explanation'] :
          describe += "; 成分解释:" + product['ingredient_explanation']
        
        # if 'place_of_origin' in product and product['place_of_origin'] :
        #   describe += "; 产地:" + product['place_of_origin']; 
        
        productDescribe += describe + "。";
    
  output_object ={
        "likeProductList" : likeProductList, 
        "recommendProducList": recommendProducList,
        "recommendProduCount" : recommendProduCount,
        "recommendCombList": recommendCombList,
        "likeCombList": likeCombList ,
        "productDescribe": productDescribe
  }
 
  return output_object
	
def transCombList(recommend_product) :
  for recommendComb in recommend_product :
    
    #productType=1 表示套装，0为单个产品
    recommendComb['productType'] = 1
    if 'top2_benefits' in recommendComb and recommendComb['top2_benefits'] :
      top2_benefits = recommendComb['top2_benefits']
      recommendCombName = "".join(top2_benefits) + "套装"
      recommendComb['comb_name'] = recommendCombName
    
    if 'product_info' in recommendComb and recommendComb['product_info'] :
      product_info_list = recommendComb['product_info']
      recommendComb['product_info'] = None
      
      productList = []
      for productItem in product_info_list :
      
        if 'id' in productItem and productItem['id']:
          productItem['id'] = transString2Int(productItem['id'])
        
        if 'benefits' in productItem and productItem['benefits']:
          benefits = productItem['benefits'];
          productItem['benefits'] = benefits.split(',')
        else :
          productItem['benefits'] = []
        
        
        if 'place_of_origin' in productItem : 
          productItem['origin'] = productItem['place_of_origin']
      
        if 'reference_price' in productItem :
          productItem['price'] = "￥" + str(productItem['reference_price']).replace(".0","")
        if 'user_score' in productItem  and productItem['user_score']:
          productItem['userScore'] = transString2Float(productItem['user_score'])
          
        productList.append(productItem)
      
      recommendComb['product'] = productList
      for product in product_info_list :
        if 'product_image' in product : 
          recommendComb['url'] = product['product_image']
          break
    
    if 'top2_benefits' in recommendComb and recommendComb['top2_benefits'] :
      recommendComb['benefits'] = recommendComb['top2_benefits'] 
      recommendComb['top2_benefits'] = None
      
  return recommend_product
	
def converProduct(prodList) :
  if prodList :
    for item in prodList : 
      
      if 'id' in item and item['id']:
        item['id'] = transString2Int(item['id'])
        
      if 'place_of_origin' in item :
        item['origin'] = item['place_of_origin']
      
      if 'reference_price' in item :
        item['price'] = "￥" + str(item['reference_price']).replace(".0","")
      if 'user_score' in item  and item['user_score']:
        item['userScore'] = transString2Float(item['user_score'])
      #productType=1 表示套装，0为单个产品
      item['productType'] = 0
      
      if 'benefits' in item and item['benefits']:
        benefits = item['benefits']
        item['benefits'] = benefits.split(',')
      else :
        item['benefits'] = []
      
  return prodList
  
def transString2Int(field) :
  if isinstance(field, int) :
    return field
  try:
    return int(field)
  except ValueError:
    return field
    

def transString2Float(field) :
  try:
    num_float = float(field)
    return num_float
  except ValueError:
    return None