# -*- coding: utf-8 -*-
import random
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
import pandas as pd
import json
from utils.plugin_tester import PluginTester
from datetime import datetime
from tools.count_tokens import count_tokens, get_v3_tokenizer
from utils.apiLLM import ApiLLM
from utils.agent_tester import AgentTester

def hfp_test(input_param, plugin_id, agent_sdk_token):
    client = PluginTester(agent_sdk_token=agent_sdk_token)

    result, time_cost = client.execute_plugin(
        plugin_id=plugin_id,
        input_param=input_param
    )

    return result, time_cost

def main():
    agent_sdk_token = "eyJhcHBJZCI6IjAwMDAwMDM0IiwidG9rZW4iOiJxNWVSSFVGQlBSNVlDWEM4OW56Wno1MU80RldqM2pCQWVwZUxmVU5vOXlLeG00SDNBTitaSWpRZmZ3RHBvWExQaE5LRXdUQlAycXVvd0laYktKWEpBdlRENU1wbmVHZWJKblFlZmJBbkRVQWNCT2tFWXNJS2dzc3FQUlVpeDVCM0JZR01PTlZMZDZYTTN3VUZLMkVhUVE9PSJ9"
    bagelToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJjcmVhdGVkIjoxNzQ5NjA0NDkzMTU0LCJ1c2VyTmFtZSI6IjE5NTI5OTc3NzkxIiwidHlwZSI6ImFjY2VzcyIsInVzZXJJZCI6Nzc4LCJkZXZpY2UiOiIiLCJqdGkiOiJjMGViNjU0Zi1mODc5LTRjYjctOWQzZi0zYzQxMWI3ZDgwODMiLCJleHAiOjE3NDk2OTA4OTMsImlhdCI6MTc0OTYwNDQ5Mywic3ViIjoiUGVyaXBoZXJhbHMiLCJpc3MiOiJkZWNpc2lvblJiYWMifQ.oa26cdCp2JrOytoCWrb6dZduyJIcCuB4HEw9yjMUCicjhH5Ha7VcY5oOglZhcGEfBszPinIYXkukGX0NYgVe5Q"
    plugin_id = "ZJa9cb14609c60"
    tokenizer = get_v3_tokenizer()

    df = pd.read_excel("workspace\护肤保健推荐理由\推荐商品-数据3.0.xlsx")
    
    # 初始化所需的列
    required_columns = ["产品信息", "回答", "字数", "API耗时", "tokens"]
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""

    for index, row in df.iterrows():

        input_param = {
            "unit": row["unit"],
            "type": row["type"],
            "skin_product": row["skin_product"],
            "nutrient_product": row["nutrient_product"],
            "userInfo": row["userInfo"],
            "require_text": row["require_text"],
            "history": row["history"],
            "query": row["query"],
            "userReport": row["userReport"],
        }


        result, time_cost = hfp_test(input_param, plugin_id, agent_sdk_token)
        
        result = json.loads(result)
        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
        # print(f"回答{index}：{result.get('data', {}).get('outPut', {}).get('content', '')}")
        df.at[index, "字数"] = len(result.get("data", {}).get("outPut", {}).get("content", ""))
        print(f"字数{index}：{len(result.get('data', {}).get('outPut', {}).get('content', ''))}")
        df.at[index, "API耗时"] = time_cost
        token = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""), tokenizer=tokenizer)
        print(f"tokens数{index}：{token}")
        df.at[index, "tokens"] = token

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"workspace\护肤保健推荐理由\护肤保健推荐理由数据_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\护肤保健推荐理由\护肤保健推荐理由数据_{timestamp}.xlsx")

def test_agent():
    tokenizer = get_v3_tokenizer()
    is_test_env = True
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1475,
            app_key="53ae2751e36c458cbbadc30a7341bf87",
            app_secret="fabdd34adbf04d6e5516100bc63626bd",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )
    
    path = "workspace\护肤保健推荐理由\评估结果_2025-06-10_20-25-11.xlsx"  #"workspace\护肤保健推荐理由\推荐商品-数据3.0.xlsx"
    path_name = os.path.splitext(path)[0]
    df = pd.read_excel(path)
    
    new_column_names = ["首token时间", "总时间", "流式输出", "字数", "tokens数"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():

        user_param = {
            "unit": row["unit"],
            "type": row["type"],
            "skin_product": row["skin_product"],
            "nutrient_product": row["nutrient_product"],
            "userInfo": row["userinfo"],
            "require_text": row["require_text"],
            "history": row["history"],
            "query": row["query"],
            "userReport": row["userReport"],
        }

        result = tester.test_stream(user_param=user_param)
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "流式输出"] = result["content"]
        df.at[index, "字数"] = len(result["content"])
        token = count_tokens(result["content"], tokenizer=tokenizer)
        df.at[index, "tokens数"] = token
        print(f"result: {result}")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    new_path = f"{path_name}_智能体结果{timestamp}.xlsx"
    df.to_excel(new_path, index=False)
    print(f"结果已保存到{new_path}")    

if __name__ == "__main__":
    # main()
    test_agent()


