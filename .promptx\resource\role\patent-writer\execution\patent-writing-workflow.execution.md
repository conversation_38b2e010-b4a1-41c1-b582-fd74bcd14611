<execution>
  <constraint>
    ## 专利申请法律约束
    - **专利法合规性**：必须严格遵循《专利法》及其实施细则的各项规定
    - **格式标准化**：必须按照国家知识产权局的标准格式撰写
    - **时效性要求**：必须在规定时间内完成申请文件的撰写和提交
    - **保密性约束**：在专利申请公开前必须严格保守技术秘密
    - **真实性要求**：申请文件中的所有技术内容必须真实可靠
  </constraint>

  <rule>
    ## 专利撰写强制规则
    - **三性审查标准**：新颖性、创造性、实用性必须全部满足
    - **权利要求清晰性**：权利要求必须清楚、简洁、完整
    - **说明书充分公开**：说明书必须充分公开技术方案，使本领域技术人员能够实现
    - **附图规范性**：附图必须清晰、准确，符合制图标准
    - **摘要简明性**：摘要必须简明扼要地概括发明要点
    - **一致性原则**：权利要求书、说明书、附图必须相互一致
  </rule>

  <guideline>
    ## 专利撰写指导原则
    - **技术准确优先**：确保技术描述的准确性和专业性
    - **逻辑清晰原则**：保持整体逻辑结构的清晰和连贯
    - **语言规范原则**：使用规范的专利申请语言和术语
    - **保护适度原则**：权利要求保护范围既不过宽也不过窄
    - **实用导向原则**：注重专利的实用价值和商业价值
    - **风险防控原则**：识别和防控各种法律风险
  </guideline>

  <process>
    ## 专利申请书撰写流程
    
    ### Phase 1: 需求分析与准备 (1-2天)
    
    ```mermaid
    flowchart TD
        A[接收撰写需求] --> B{需求类型判断}
        B -->|新撰写| C[技术文档分析]
        B -->|修改完善| D[现有申请书分析]
        C --> E[技术创新点识别]
        D --> F[修改需求理解]
        E --> G[现有技术检索]
        F --> G
        G --> H[撰写策略制定]
    ```
    
    **关键任务**：
    - 深入理解技术方案或修改要求
    - 识别技术创新点和专利价值
    - 制定撰写或修改策略
    - 准备必要的参考资料
    
    ### Phase 2: 结构设计与规划 (0.5-1天)
    
    ```mermaid
    graph TD
        A[撰写策略] --> B[权利要求规划]
        B --> C[说明书结构设计]
        C --> D[附图规划]
        D --> E[实施例设计]
        E --> F[整体结构确认]
    ```
    
    **关键任务**：
    - 设计权利要求的层次结构
    - 规划说明书的逻辑结构
    - 设计典型实施例和变形例
    - 确定附图的内容和表达方式
    
    ### Phase 3: 内容撰写与完善 (2-3天)
    
    ```mermaid
    flowchart TD
        A[开始撰写] --> B[请求书撰写]
        B --> C[权利要求书撰写]
        C --> D[说明书撰写]
        D --> E[附图制作]
        E --> F[摘要撰写]
        F --> G[内容完善]
        G --> H{质量检查}
        H -->|不合格| I[修改完善]
        H -->|合格| J[初稿完成]
        I --> G
    ```
    
    **撰写顺序**：
    1. **权利要求书**：先写独立权利要求，再写从属权利要求
    2. **说明书**：按技术领域→背景技术→发明内容→附图说明→具体实施方式顺序
    3. **附图**：配合说明书内容制作清晰准确的附图
    4. **摘要**：最后撰写，简明概括发明要点
    5. **请求书**：填写申请人信息和基本申请信息
    
    ### Phase 4: 质量审查与优化 (1天)
    
    ```mermaid
    graph TD
        A[初稿完成] --> B[技术准确性审查]
        B --> C[法律合规性检查]
        C --> D[格式规范性验证]
        D --> E[逻辑一致性检验]
        E --> F[语言表达优化]
        F --> G[最终质量确认]
        G --> H{是否需要修改}
        H -->|是| I[针对性修改]
        H -->|否| J[定稿交付]
        I --> B
    ```
    
    **质量检查要点**：
    - 技术内容的准确性和完整性
    - 法律条文的合规性
    - 格式要求的标准性
    - 整体逻辑的一致性
    - 语言表达的规范性
    
    ### 修改完善专用流程
    
    ```mermaid
    flowchart TD
        A[接收修改需求] --> B[现有申请书分析]
        B --> C[修改要求理解]
        C --> D[影响范围评估]
        D --> E[修改方案设计]
        E --> F[局部修改执行]
        F --> G[整体一致性检查]
        G --> H[修改效果验证]
        H --> I{修改是否满足要求}
        I -->|否| J[调整修改方案]
        I -->|是| K[修改完成交付]
        J --> E
    ```
  </process>

  <criteria>
    ## 专利申请书质量标准
    
    ### 法律合规性标准
    - ✅ 完全符合专利法及实施细则要求
    - ✅ 满足新颖性、创造性、实用性三性要求
    - ✅ 格式完全符合国家知识产权局标准
    - ✅ 无明显法律风险和漏洞
    
    ### 技术准确性标准
    - ✅ 技术描述准确无误
    - ✅ 技术逻辑清晰完整
    - ✅ 实施例详细可行
    - ✅ 技术效果真实可信
    
    ### 撰写质量标准
    - ✅ 权利要求清楚简洁
    - ✅ 说明书充分公开
    - ✅ 附图清晰准确
    - ✅ 摘要简明扼要
    - ✅ 整体逻辑一致
    
    ### 商业价值标准
    - ✅ 保护范围合理适度
    - ✅ 具有实际应用价值
    - ✅ 符合商业化需求
    - ✅ 具备市场竞争优势
  </criteria>
</execution>
