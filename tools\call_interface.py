import requests

def call_interface(url, client_params):
    """
    测试clife服务接口
    """
    print(f"=====================测试服务接口: {url}=====================")
    headers = {'content-type': 'application/json'}
    re = requests.post(url, json=client_params, headers=headers)
    text = re.text
    return text

if __name__ == "__main__":
    url = "https://open.api.clife.cn/ai-service/ca/v2.0/health-life-advice"
    client_params = {
    "elderlyData": "慢性病情况：骨质疏松症、高血压、高脂血症；\n行为习惯：入睡困难、独来独往；\n兴趣爱好：养花、象棋；\n饮食偏好：喜好松软、爽口，口味咸；\n微量元素：缺铁、缺钙；\n过敏史：鱼、虾；\n疾病史：胃溃疡；",
    "environmentData": "季节：冬季；\n24节气：冬至；\n温度：15摄氏度；\n湿度：87%；\n天气情况：阴天多云；\n空气质量等级：良；",
    "llmSelect" : 1
    }
    print(call_interface(url, client_params))
