import pandas as pd
import os
import sys
from datetime import datetime
import json
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
sys.path.append(project_root)
from utils.agent_tester import AgentTester

def main():
    # bagel_token = os.getenv("BAGEL_TOKEN")
    is_test_env = False
    if is_test_env:
        tester = AgentTester(
            micro_app_id=1597,
            app_key="c1672d938ba64d55807b939226a80daf",
            app_secret="0b08506df86680bbb7e6e63206f763a5",
            is_test_env=is_test_env
    )
    else:
        tester = AgentTester(
            micro_app_id=314,
            app_key="3598fc906dbb41d8bcbeae9ef2e98121",
            app_secret="884be81a1f4f81b98d8d5bbbd34edd56",
            is_test_env=is_test_env
    )

    df = pd.read_excel("workspace\童言童语\儿童语言能力评价测试数据.xlsx")
    new_column_names = ["首token时间", "总时间", "输出"]
    for col in new_column_names:
        df[col] = ""
    for index, row in df.iterrows():
        print(f"-----------------处理第 {index} 行---------------------------")
        evaluation_dimension = row["维度"]
        user_content = row["语音文本内容"]
        
        user_param = {
            "evaluation_dimension": evaluation_dimension,
            "user_content": user_content,
            "user_id": row["user_id"],
            "user_name": row["user_name"],
            "user_age": row["user_age"],
            "user_gender": row["user_gender"],
            "user_recent_result": row["user_recent_result"],  
            "class_performance": row["class_performance"]
        }

        result = tester.test_no_stream(user_param=user_param)
        print(f"result: {result}")
        df.at[index, "首token时间"] = result["first_token_time"]
        df.at[index, "总时间"] = result["total_time"]
        df.at[index, "输出"] = json.loads(json.loads(result["content"][6:]).get("data", {}).get("agentResponse", "{}")).get("output_f")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")   
    df.to_excel(f"workspace\童言童语\儿童语言能力评价测试数据copy_智能体结果_{timestamp}.xlsx", index=False)
    print(f"结果已保存到workspace\童言童语\儿童语言能力评价测试数据copy_智能体结果_{timestamp}.xlsx")

if __name__ == "__main__":
    main()
