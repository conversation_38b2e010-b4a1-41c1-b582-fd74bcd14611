import pandas as pd
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 获取上一级目录（original_folder）
project_root = os.path.dirname(parent_dir)
sys.path.append(project_root)
from tools.count_tokens import count_tokens
from utils.plugin_tester import PluginTester
from datetime import datetime
import json

def test_plugin(chat, history, businessDomain, app_name, emotional_type):
    client = PluginTester(agent_sdk_token=os.getenv("AGENT_SDK_TOKEN"))
    bagel_token = os.getenv("BAGEL_TOKEN")

    input_param = {
        "chat": chat,
        "history": history,
        # "themeId": "",
        "bagelToken": bagel_token,
        # "micro_app_id": '1',
        "businessDomain": businessDomain,
        "emotional_type": emotional_type,
        "emotional_strength": '中',
        "app_name": app_name,
    }

    result, time_cost = client.execute_plugin(
        plugin_id="ZJ1973dc41a30b", # 闲聊V3-ZJb1ec0b82d7fc 无关问题V3-ZJ1973dc41a30b
        input_param=input_param
    )

    return result, time_cost

def main():
    df = pd.read_excel("workspace\个性化_test\个性化数据集.xlsx")
    # 添加新的列用于存储API解析结果
    new_columns = ["API结果", "API耗时", "回答"]
    for col in new_columns:
        if col not in df.columns:
            df[col] = ""
    
    for index, row in df.iterrows():
        chat = row["输入"]
        history = []
        businessDomain = row["businessDomain"]
        app_name = row["智能体类型"]
        emotional_type = row["情感类型"]
        result, time_cost = test_plugin(chat, history, businessDomain, app_name, emotional_type)

        result = json.loads(result)
        df.at[index, "输入token"] = count_tokens(chat)
        df.at[index, "输出token"] = count_tokens(result.get("data", {}).get("outPut", {}).get("content", ""))
        df.at[index, "API结果"] = result
        df.at[index, "API耗时"] = time_cost
        df.at[index, "回答"] = result.get("data", {}).get("outPut", {}).get("content", "")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    df.to_excel(f"个性化_test/个性化数据集_结果_{timestamp}.xlsx", index=False)
    print(f"结果已保存到个性化_test/个性化数据集_结果_{timestamp}.xlsx")



if __name__ == "__main__":
    main()


