import os
from PIL import Image
import matplotlib.pyplot as plt

def analyze_images(folder_path):
    """
    解析文件夹中图片尺寸,并绘制散点图
    """
    # 获取文件夹中的所有文件
    files = [f for f in os.listdir(folder_path) if f.endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif'))]
    
    # 初始化最大和最小尺寸
    max_size = (0, 0)
    min_size = (float('inf'), float('inf'))
    
    # 存储所有图片的尺寸数据
    widths = []
    heights = []
    filenames = []
    
    # 遍历每个图片文件
    for file in files:
        image_path = os.path.join(folder_path, file)
        with Image.open(image_path) as img:
            # 获取图片尺寸
            width, height = img.size
            print(f"图片 {file} 的尺寸: {width}x{height}")
            
            # 存储数据用于绘图
            widths.append(width)
            heights.append(height)
            filenames.append(file)
            
            # 更新最大尺寸
            if width * height > max_size[0] * max_size[1]:
                max_size = (width, height)
            
            # 更新最小尺寸
            if width * height < min_size[0] * min_size[1]:
                min_size = (width, height)
    
    print(f"\n所有图片中的最大尺寸: {max_size[0]}x{max_size[1]}")
    print(f"所有图片中的最小尺寸: {min_size[0]}x{min_size[1]}")
    
    # 创建散点图
    plt.figure(figsize=(10, 8))
    plt.scatter(widths, heights, alpha=0.6)
    
    # 添加图片文件名标签
    for i, txt in enumerate(filenames):
        plt.annotate(txt, (widths[i], heights[i]), fontsize=8)
    
    # 设置图表属性
    plt.title('size of images')
    plt.xlabel('width (pixel)')
    plt.ylabel('height (pixel)')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加最大和最小尺寸的标记
    plt.scatter([max_size[0]], [max_size[1]], color='red', s=100, label='最大尺寸')
    plt.scatter([min_size[0]], [min_size[1]], color='green', s=100, label='最小尺寸')
    
    plt.legend()
    plt.show()

if __name__ == "__main__":
    # 使用示例
    folder_path = 'C:/Users/<USER>/Desktop/测试菜谱图片'  
    analyze_images(folder_path)