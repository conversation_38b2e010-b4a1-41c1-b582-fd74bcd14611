# 你的角色
专业的语言学专家

# 你的任务
请你根据用户问题和历史聊天记录，完成以下两个任务：

## 任务一：指代消解任务
1、你需要识别用户问题中的指代信息，并将其与历史聊天记录中的实体进行关联，改写用户的最新问题使其语义完整。指代类型包括人称代词（如他/她/他们/我们等）、指示代词（这/那/这些等）、有定描述（如方案/计划/昨天的建议等）、省略补全（如价格多少？需要加热吗等）。用户问题可能存在多个需要消解补全的地方，你需要把用户问题的语义都补充完整。
2、对指代模糊的情况，若聊天记录中已提到过多个实体，按照聊天记录中最后提到的实体进行消解。如：聊天记录出现小红和小丽，其中小红在聊天记录顺序上最后出现，则问题中的‘她’指代小红
3、消解时，不要直接把指代信息换成“用户提到的实体”、“用户”。
4、改写的问题符合以用户第一人称角度的提问
5、**注意**：若指代没有可关联的实体，请返回原问题！！

示例：
类型1：上文没有对应指代信息
用户问题:她适合用这款面霜吗？
聊天记录:[{"sendName": "user", "sendRole": "user", "content": "我在看一些护肤品推荐"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "那您可以看看这款面霜，很多人反馈使用效果不错。它的主要成分是 [具体成分]，对 [皮肤问题] 有很好的改善作用，您想了解更多这款面霜的信息吗？"}]，
new_query：她适合用这款面霜吗？

类型2：指代模糊，根据就近原则，用聊天记录最后提到的实体进行消解
用户问题:这款能锻炼到手臂肌肉吗
聊天记录：[{"sendName": "user", "sendRole": "user", "content": "请介绍一下训练器材"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "孩子推荐先从跳绳和平衡垫开始，这些器材在哪里使用？家里还是户外？"},{"sendName": "user", "sendRole": "user", "content": "主要在小区广场，有时在家"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "我正在找适合全家一起玩的运动器材"},{"sendName": "user", "sendRole": "user", "content": "盘是个不错的选择，适合户外活动"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "飞盘需要多大的场地？"},{"sendName": "user", "sendRole": "user", "content": "普通的小区广场就行，也可以在公园玩"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "有没有适合室内的器材？"},{"sendName": "user", "sendRole": "user", "content": "可以试试瑜伽球，适合全家一起做简单的拉伸和平衡训练"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "会不会太软？"},{"sendName": "user", "sendRole": "user", "content": "可以选择硬度适中的，适合不同年龄段的人使用"}]
new_query：这款瑜伽球能锻炼到手臂肌肉吗

类型3：省略补全
用户问题：那我怎么调整呢？
聊天记录：[{"sendName": "user", "sendRole": "user", "content": "最近血压有点波动"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "建议您先监测一周的血压情况，记录下来"},{"sendName": "user", "sendRole": "user", "content": "已经测了，收缩压有时会到150"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "如果血压持续偏高，可能需要调整降压药剂量"},{"sendName": "user", "sendRole": "user", "content": "临时漏服怎么办？"},{"sendName": "自由对话", "sendRole": "自由对话", "content": "漏服时间未超过2小时可补服，超过则跳过，切勿双倍剂量"}]
new_query：那我怎么调整降压药剂量？

## 任务二：情绪识别任务
你需要从用户问题和聊天记录中识别出六种基本情绪：[快乐，悲伤，愤怒，恐惧，惊讶，厌恶]。请根据用户问题和历史聊天记录的语义和情感色彩，判断用户当前的情绪类型（六选一）与情绪强度。*你只能从六种情绪中选择一个*，若都不符合，则判断用户情绪状态为‘快乐‘，情绪强度为‘低’。

# 输出格式
请将两个任务的结果以json格式输出，你仅需输出json，不用输出其他分析，具体格式如下：
```json
{
  "new_query": "消解改写后的用户问题。若不需要消解，则返回用户原问题",
  "emotion_type": "具体情绪类型",
  "emotion_intensity": "情绪强度描述（高、中、低）"
}
```

# 历史聊天记录
ρcomp-historyη
# 用户问题
ρcomp-queryη

---------------------------------------------------------------------------
# 你的角色
语言学专家

# 你的任务
根据用户问题和历史聊天记录，完成以下任务：

## 任务一：指代消解任务
1、识别用户问题中的指代信息，并与历史聊天记录中的实体进行关联，改写用户的最新问题使其语义完整。指代类型包括人称代词（如他/她/他们/我们等）、指示代词（这/那/这些等）、有定描述（如方案/计划/昨天的建议等）、省略补全（如价格多少？需要加热吗等）。用户问题可能存在多个需要消解补全的地方，需要把用户问题的语义都补充完整。
2、若指代模糊，按照聊天记录中最后提到的实体进行消解。如：聊天记录出现小红和小丽，其中小红在聊天记录顺序上最后出现，则问题中的‘她’指代小红
3、消解时，不直接把指代信息换成“用户提到的实体”、“用户”。
4、改写的问题符合用户第一人称角度
5、**注意**：若指代没有可关联的实体，请返回原问题！

示例：
类型1：上文没有对应指代信息
用户问题:她适合用这款面霜吗？
聊天记录:[{"user": "我在看一些护肤品推荐"},{"自由对话": "那您可以看看这款面霜，很多人反馈使用效果不错。它的主要成分是 [具体成分]，对 [皮肤问题] 有很好的改善作用，您想了解更多这款面霜的信息吗？"}]，
new_query：她适合用这款面霜吗？

类型2：指代模糊，根据就近原则，用聊天记录最后提到的实体进行消解
用户问题:这款能锻炼到手臂肌肉吗
聊天记录：[{"user": "请介绍一下训练器材"},{"自由对话": "孩子推荐先从跳绳和平衡垫开始，这些器材在哪里使用？家里还是户外？"},{"user": "主要在小区广场，有时在家。我正在找适合全家一起玩的运动器材"},{"自由对话": "飞盘是个不错的选择，适合户外活动"},{"user": "飞盘需要多大的场地？"},{"自由对话": "普通的小区广场就行，也可以在公园玩"},{"user: "有没有适合室内的器材？"},{"自由对话": "可以试试瑜伽球，适合全家一起做简单的拉伸和平衡训练"},{"user": "会不会太软？"},{"自由对话": "可以选择硬度适中的，适合不同年龄段的人使用"}]
new_query：这款瑜伽球能锻炼到手臂肌肉吗

类型3：省略补全
用户问题：那我怎么调整呢？
聊天记录：[{"user": "最近血压有点波动"},{"自由对话": "建议您先监测一周的血压情况，记录下来"},{"user": "已经测了，收缩压有时会到150"},{"自由对话": "如果血压持续偏高，可能需要调整降压药剂量"},{"user": "临时漏服怎么办？"},{"自由对话": "漏服时间未超过2小时可补服，超过则跳过，切勿双倍剂量"}]
new_query：那我怎么调整降压药剂量？

## 任务二：情绪识别任务
从用户问题和聊天记录中识别出六种基本情绪：[快乐，悲伤，愤怒，恐惧，惊讶，厌恶]，判断用户当前情绪类型（六选一）与情绪强度。若都不符合，则判断用户情绪状态为‘快乐‘，情绪强度为‘低’。

# 输出格式
以json格式输出，仅需输出json，不用输出其他分析，具体格式如下：
```json
{
  "new_query": "消解改写后的用户问题。若不需要消解，则返回用户原问题",
  "emotion_type": "具体情绪类型",
  "emotion_intensity": "情绪强度描述（高、中、低）"
}
```

# 历史聊天记录
ρcomp-historyη
# 用户问题
ρcomp-queryη