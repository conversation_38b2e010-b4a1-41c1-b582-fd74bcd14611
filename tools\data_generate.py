import sys
import os
import json
import pandas as pd
from typing import List, Dict
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
from utils.apiLLM import ApiLLM
from tools.str_2_json import to_json

# 报告分析聊天数据生成
report_analysis_prompt = """
请根据以下智能体类别生成对话数据，最终用户会要求进行分析。对话需要符合该智能体的专业领域特点。

智能体类别：{agent_type}

对话要求：
1. 对话必须围绕{agent_type}相关话题展开
2. 对话至少包含3轮以上的交互
3. 对话要体现用户对该领域的关注和需求
4. 最后用户query必须是请求分析的表达
5. 对话要自然流畅，符合实际使用场景

请根据智能体类别特点生成对话：

1. 运动智能体：
   - 关注运动习惯、运动强度、运动效果
   - 涉及运动计划、运动建议、运动数据分析
   - 最终要求生成运动分析报告

2. 皮肤智能体：
   - 关注皮肤状况、护肤习惯、产品使用
   - 涉及皮肤问题、护肤建议、产品推荐
   - 最终要求生成皮肤分析报告

3. 睡眠智能体：
   - 关注睡眠质量、睡眠习惯、睡眠环境
   - 涉及睡眠问题、改善建议、睡眠数据分析
   - 最终要求生成睡眠分析报告

请直接返回JSON格式的数据，不要包含其他说明文字。
"""

def generate_chat_data(prompt: str):
    """
    使用大模型生成聊天数据
    
    Args:
        prompt: 提示词
        num_samples: 需要生成的样本数量
    
    Returns:
        包含query和history的字典列表
    """
    # 初始化LLM
    llm = ApiLLM(
        model="doubao-deepseek-v3",
        completion_url="http://10.6.14.2:30000/module/byte/getChatCompletions",
        stream_url="http://10.6.14.2:30000/module/byte/getChatCompletionsStream",
        temperature=0.9,
        app_id='00000020',
        micro_app_id=199,
        micro_app_test=True
    )
    
    # 构建系统提示词
    system_prompt = """请根据要求，生成1条聊天数据，每条数据包含一个query和对应的history。
    数据格式要求为JSON格式，示例：    
    {
        "query": "用户的问题",
        "history": [
        {
            "sendName": "user",
            "sendRole": "user",
            "content": "最近皮肤特别干燥，有没有推荐的乳液或者面霜？"
        },
        {
            "sendName": "assistant", 
            "sendRole": "assistant",
            "content": "可以试试欧莱雅的保湿乳液，它含有透明质酸，能够深层滋润皮肤，而且质地轻薄，不会觉得油腻。如果你需要更滋润的效果，也可以搭配使用欧莱雅的保湿面霜，它能更好地锁住水分，让皮肤一整天都保持水润。"
        }
        ]
    }
    
    请确保生成的数据符合以下要求：
    1. 每条数据都是独立的对话
    2. history中的对话要连贯，且history最后必须为assistant的回答
    3. 返回的数据必须是合法的JSON格式
    4. 数据要符合实际对话场景
    5. history对话轮次至少3轮
    
    请直接返回JSON格式的数据，不要包含其他说明文字。
    """
   
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": prompt}
    ]
    
    try:
        response = llm(messages=messages, stream=False)
        content = response.choices[0].message.content
        # 解析JSON
        try:
            data = to_json(content)
            print(f"生成的数据: {data}")
            return data
        except json.JSONDecodeError:
            print("JSON解析失败，请检查生成的数据格式")
            return []
            
    except Exception as e:
        print(f"生成数据时发生错误: {str(e)}")
        return []

def save_to_excel(data):
    """
    将生成的对话数据保存到Excel文件中
    
    Args:
        data: 包含query和history的字典
    """
    # 将单个字典转换为列表
    if isinstance(data, dict):
        data = [data]
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 将history列转换为字符串格式
    df['history'] = df['history'].apply(lambda x: json.dumps(x, ensure_ascii=False))
    
    # 保存到Excel文件
    output_file = os.path.join(project_root, 'data', 'generated_chat_data.xlsx')
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 保存数据，不包含索引列
    df.to_excel(output_file, index=False)
    print(f"数据已保存到: {output_file}")

def main():
    """
    主函数：为不同类型的智能体生成对话数据并保存到Excel
    """
    # 定义智能体类型
    agent_types = ["运动", "睡眠", "皮肤"]
    
    # 存储所有生成的数据
    all_data = []
    
    # 为每种智能体生成数据
    for _ in range(10):
        for agent_type in agent_types:
            print(f"正在为{agent_type}智能体生成数据...")
            # 使用对应的智能体类型生成数据
            prompt = report_analysis_prompt.format(agent_type=agent_type)
            data = generate_chat_data(prompt)
            
            if data:
                all_data.append(data)
                print(f"{agent_type}智能体数据生成成功")
            else:
                print(f"{agent_type}智能体数据生成失败")
    
    # 保存所有数据到Excel
    if all_data:
        save_to_excel(all_data)
        print("所有数据已保存到Excel")
    else:
        print("未生成任何有效数据")

if __name__ == "__main__":
    main()
